import os
import random
import time
from math import ceil
import requests
import pandas as pd
from docx import Document
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
from docx.shared import RGBColor
import re
from datetime import datetime

headers = {
    "Host": "applhb.longhuvip.com"
}

headers1 = {
    "Content-Length": "186"
}

def get_json_data(url, post_data, headers, retries=5, backoff_factor=0.3):
    for attempt in range(retries):
        try:
            json_data = requests.post(url, data=post_data, headers=headers).json()
            return json_data
        except requests.exceptions.RequestException as e:
            print(f'请求失败，正在尝试重新请求（尝试次数{attempt + 1}/{retries})，错误信息:{e}')
            time.sleep(backoff_factor * (2 ** attempt))
    else:
        print("请求失败次数过多，放弃请求")
        return None

def set_line_spacing(paragraph, spacing):
    p = paragraph._element
    pPr = p.get_or_add_pPr()
    spacing_element = OxmlElement('w:spacing')
    spacing_element.set(qn('w:line'), str(spacing))
    pPr.append(spacing_element)

def set_paragraph_background(paragraph, color):
    run = paragraph.runs[0]
    rPr = run._element.get_or_add_rPr()
    shd = OxmlElement('w:shd')
    shd.set(qn('w:val'), 'clear')
    shd.set(qn('w:color'), 'auto')
    shd.set(qn('w:fill'), color)
    rPr.append(shd)

def set_text_properties(paragraph, color, bold):
    for run in paragraph.runs:
        run.font.color.rgb = RGBColor.from_string(color)
        run.bold = bold

def clean_text(text):
    return re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)

def main(StockID, Name, Day, doc):
    url = "https://applhb.longhuvip.com/w1/api/index.php?apiv=w36&PhoneOSNew=1&VerSion=********"
    paragraph = doc.add_paragraph(f'{Name} {StockID}  \n', style='Heading 4')
    set_line_spacing(paragraph, 240)  # 设置行距为0.5倍
    set_paragraph_background(paragraph, 'FFFF00')  # 设置背景颜色为黄色

    num = 0
    count = 1
    while True:
        post_data = {
            'c': 'Comments',
            'a': 'Get',
            'Index': num,
            'st': '30',
            'StockID': StockID,
            'Day': Day,
            'Type': '1',
            'Tsort': '0',
            'DeviceID': '85A9DB40FB9144A3AF9B776A113B2AED61f945f9b6071bb7b0123470fb925e7e'
        }
        json_data = get_json_data(url, post_data, headers)
        if json_data:
            if json_data.get('List', []) == []:
                break
            else:
                num += 30
            for data in json_data['List']:
                ID = data['ID']
                Message = clean_text(data['Message'])
                ReplyNum = data['ReplyNum']

                if Message not in ["niubility", "&#039;"] and len(Message) >= 7:
                    print(Message)
                    paragraph = doc.add_paragraph(f'  {Message}')
                    set_line_spacing(paragraph, 240)  # 设置行距为0.5倍
                    set_text_properties(paragraph, '4F81BD', False)  # 设置文字颜色为#4F81BD且加粗

                ReplyList = data['ReplyList']
                if ReplyList and int(ReplyNum) <= 3:
                    for reply in data['ReplyList']:
                        reply_Message = clean_text(reply['Message'])
                        if reply_Message not in ["niubility", "66"] and len(reply_Message) >= 7:
                            print(reply_Message)
                            paragraph = doc.add_paragraph(f'    {reply_Message}')
                            set_line_spacing(paragraph, 240)  # 设置行距为0.5倍

                if int(ReplyNum) > 3:
                    for page_ in range(ceil(int(ReplyNum) / 20) + 1):
                        chile_comment = {
                            'c': 'Stock',
                            'a': 'GetReply',
                            'StockID': StockID,
                            'Day': Day,
                            'ID': ID,
                            'Index': page_ * 20,
                            'st': '20',
                            'DeviceID': '85A9DB40FB9144A3AF9B776A113B2AED61f945f9b6071bb7b0123470fb925e7e'
                        }
                        json_data = get_json_data(url, chile_comment, headers)
                        if json_data:
                            for data in json_data.get('List', []):
                                reply_Message = clean_text(data['Message'])
                                if reply_Message not in ["niubility", "66"] and len(reply_Message) >= 7:
                                    print(reply_Message)
                                    paragraph = doc.add_paragraph(f'    {reply_Message}')
                                    set_line_spacing(paragraph, 240)  # 设置行距为0.5倍
                        time.sleep(random.random())
                    time.sleep(random.random())
            count += 1

def get_index(date):
    url = 'https://applhb.longhuvip.com/w1/api/index.php'
    post_data = {
        'st': '500',
        'a': 'GetStockList',
        'c': 'LongHuBang',
        'PhoneOSNew': '1',
        'DeviceID': '85A9DB40FB9144A3AF9B776A113B2AED61f945f9b6071bb7b0123470fb925e7e',
        'VerSion': '********',
        'Token': '0',
        'Time': date,
        'Index': '0',
        'apiv': 'w36',
        'Type': '2',
        'UserID': '0&'
    }
    index_data = get_json_data(url, post_data, headers1)
    return index_data


if __name__ == '__main__':
    # 获取今天的日期，并格式化为"YYYY-MM-DD"
    today = datetime.now().strftime('%Y-%m-%d')
    date = today  # 将今天的日期赋值给date变量

    # 假设这里不再需要从Excel文件中读取日期，所以下面的代码被注释掉
    # excel_path = r'E:\BaiduNetdiskDownload\日期.xlsx'
    # df = pd.read_excel(excel_path, header=None)

    # 因为我们不再遍历Excel文件，所以下面的循环也被注释掉
    # for index, row in df.iterrows():
    #     for date in row.dropna():  # 这行也不再需要

    # 直接使用上面获取的date值
    index_data = get_index(date)  # 假设这个函数返回与给定日期相关的数据
    if index_data:
        docx_name = f'{date} 龙虎榜评论.docx'
        if os.path.exists(docx_name):
            doc = Document(docx_name)  # 打开现有文档
        else:
            doc = Document()  # 创建新文档

        if 'list' in index_data:
            for data in index_data['list'][:20]:
                ID = data['ID']
                Name = data['Name']
                print(ID, Name)
                main(ID, Name, date, doc)
                time.sleep(random.random())

        doc.save(docx_name)
