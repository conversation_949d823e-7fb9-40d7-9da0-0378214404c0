from flask import Flask, render_template, request, jsonify
import requests
import pandas as pd
from datetime import datetime, timedelta
import os

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/download', methods=['POST'])
def download():
    try:
        data = request.json
        print("接收到的请求数据:", data)  # 添加日志
        
        date_type = data['dateType']
        r_start = data['rStart']
        r_end = data['rEnd']
        download_type = data['downloadType']
        start_page = int(data['startPage'])
        end_page = int(data['endPage'])
        
        all_data = []
        total_records = 0
        processed_dates = []  # 记录处理成功的日期
        
        # 处理日期范围
        if date_type == 'history':
            start_date = datetime.strptime(data['startDate'], '%Y-%m-%d')
            end_date = datetime.strptime(data['endDate'], '%Y-%m-%d')
            date_range = []
            
            # 生成日期范围
            current_date = start_date
            while current_date <= end_date:
                date_range.append(current_date.strftime('%Y-%m-%d'))
                current_date += timedelta(days=1)
            print(f"将处理的日期范围: {date_range}")  # 添加日志
        else:
            date_range = ['']  # 实时数据
            
        # 遍历每个日期
        for date in date_range:
            try:
                current_page_data = []  # 用于存储当前页的数据
                
                if download_type == 'specific':
                    pages = range(start_page, end_page + 1)
                else:  # 全部下载
                    pages = range(0, 1000)  # 设置一个较大的范围，直到没有数据为止
                    
                for page in pages:
                    url = f'https://apphq.longhuvip.com/w1/api/index.php'
                    params = {
                        'Order': '1',
                        'a': 'RealRankingInfo_W8',
                        'st': '60',
                        'c': 'NewStockRanking',
                        'PhoneOSNew': '1',
                        'RStart': r_start,
                        'DeviceID': '20ad85ca-becb-3bed-b3d4-30032a0f5923',
                        'VerSion': '*******',
                        'index': page * 60,
                        'REnd': r_end,
                        'apiv': 'w29',
                        'Type': '1',
                        'FilterMotherboard': '0',
                        'Filter': '0',
                        'Ratio': '6',
                        'FilterTIB': '0',
                        'FilterGem': '0',
                        'Date': date
                    }
                    
                    print(f"请求URL参数: {params}")  # 添加日志
                    response = requests.get(url, params=params)
                    response_json = response.json()
                    print(f"API响应: {response_json}")  # 添加日志
                    
                    if 'list' in response_json and response_json['list']:
                        current_page_data.extend(response_json['list'])
                        print(f"获取到第{page}页数据，条数: {len(response_json['list'])}")  # 添加日志
                    else:
                        print(f"第{page}页没有数据，停止获取")  # 添加日志
                        if download_type == 'all':
                            break
                
                # 每个日期的数据处理完后，保存到对应的文件
                if current_page_data:
                    df = pd.DataFrame(current_page_data)
                    
                    # 定义列映射和处理逻辑
                    column_mappings = {
                        0: {'name': '股票代码', 'process': None},
                        1: {'name': '股票名称', 'process': None},
                        4: {'name': '板块', 'process': None},
                        13: {'name': '区间净额', 'process': lambda x: round(float(x)/10000, 2) if x else 0},
                        7: {'name': '区间成交', 'process': lambda x: round(float(x)/100000000, 2) if x else 0},
                        5: {'name': '价格', 'process': None},
                        6: {'name': '当前涨幅', 'process': None},
                        11: {'name': '主力买', 'process': lambda x: round(float(x)/100000000, 2) if x else 0},
                        12: {'name': '主力卖', 'process': lambda x: round(float(x)/100000000, 2) if x else 0},
                        37: {'name': '总市值', 'process': lambda x: round(float(x)/100000000, 2) if x else 0},
                        38: {'name': '流通市值', 'process': lambda x: round(float(x)/100000000, 2) if x else 0},
                        10: {'name': '实际流通', 'process': lambda x: round(float(x)/100000000, 2) if x else 0},
                        8: {'name': '实际换手', 'process': None},
                        14: {'name': '买成占比', 'process': None},
                        15: {'name': '卖成占比', 'process': None},
                        16: {'name': '净成占比', 'process': None},
                        17: {'name': '买流占比', 'process': None},
                        18: {'name': '卖流占比', 'process': None},
                        19: {'name': '净流占比', 'process': None},
                        25: {'name': '换手率', 'process': None}
                    }

                    # 处理数据并重命名列
                    new_df = pd.DataFrame()
                    for col_idx, mapping in column_mappings.items():
                        if col_idx < len(df.columns):
                            if mapping['process']:
                                new_df[mapping['name']] = df[col_idx].apply(mapping['process'])
                            else:
                                new_df[mapping['name']] = df[col_idx]

                    # 使用日期作为文件名
                    filename = f"{date if date else datetime.now().strftime('%Y-%m-%d')}.xlsx"
                    new_df.to_excel(filename, index=False)
                    print(f"保存文件: {filename}")  # 添加日志
                    
                    total_records += len(current_page_data)
                    processed_dates.append(date)
                    
            except Exception as e:
                print(f"处理日期 {date} 时出错: {str(e)}")  # 添加日志
                continue
        
        if total_records > 0:
            success_message = f'数据已保存到文件，共处理{len(processed_dates)}个日期，成功日期：{", ".join(processed_dates)}'
            print(success_message)  # 添加日志
            return jsonify({
                'success': True, 
                'message': success_message,
                'total_records': total_records
            })
        else:
            error_message = '未获取到数据'
            print(error_message)  # 添加日志
            return jsonify({'success': False, 'message': error_message})
            
    except Exception as e:
        error_message = f'发生错误: {str(e)}'
        print(error_message)  # 添加日志
        return jsonify({'success': False, 'message': error_message})

if __name__ == '__main__':
    if not os.environ.get('WERKZEUG_RUN_MAIN'):
        print("\n" + "="*50)
        print("请访问以下地址进入下载页面：")
        print("http://127.0.0.1:5000")
        print("="*50 + "\n")
    app.run(debug=True) 