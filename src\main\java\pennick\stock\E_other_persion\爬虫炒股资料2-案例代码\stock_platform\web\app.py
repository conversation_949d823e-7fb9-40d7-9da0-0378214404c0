"""
股票数据平台主应用 - 完整功能版本
"""
from flask import Flask, render_template, request, jsonify, send_file
import sys
import os
import logging
from datetime import datetime
import threading
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import WEB_CONFIG, DATA_SOURCES, create_directories
from core.base_crawler import crawler_manager
from core.data_processor import data_processor
from crawlers.kaipanla_crawler import KaipanlaCrawler

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'stock_platform_secret_key'

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化
def init_app():
    """初始化应用"""
    try:
        # 创建目录
        create_directories()
        
        # 注册爬虫
        crawler_manager.register_crawler(KaipanlaCrawler())
        
        logger.info("应用初始化完成")
        
    except Exception as e:
        logger.error(f"应用初始化失败: {e}")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', 
                         data_sources=DATA_SOURCES,
                         today=datetime.now().strftime('%Y-%m-%d'))

@app.route('/api/crawlers')
def get_crawlers():
    """获取爬虫列表"""
    try:
        crawlers = []
        for name, config in DATA_SOURCES.items():
            crawler_info = {
                "name": name,
                "display_name": config["name"],
                "description": config["description"],
                "status": "ready"
            }
            
            # 添加特殊配置
            if name == "kaipanla":
                crawler_info["data_types"] = config.get("data_types", [])
                
            crawlers.append(crawler_info)
            
        return jsonify({"success": True, "crawlers": crawlers})
        
    except Exception as e:
        logger.error(f"获取爬虫列表失败: {e}")
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/crawl/sync', methods=['POST'])
def crawl_data_sync():
    """同步执行爬虫（等待结果）"""
    try:
        data = request.get_json()
        crawler_name = data.get('crawler')
        params = data.get('params', {})
        
        logger.info(f"同步执行爬虫: {crawler_name}, 参数: {params}")
        
        # 同步执行爬虫
        result = crawler_manager.run_crawler(crawler_name, **params)
        
        if result["success"]:
            # 自动导出数据
            if result.get("data_count", 0) > 0:
                crawler = crawler_manager.get_crawler(crawler_name)
                if crawler and crawler.results:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{crawler_name}_{timestamp}"
                    
                    # 根据爬虫类型选择数据处理方式
                    if crawler_name == "kaipanla":
                        data_type = params.get("data_type", "涨停")
                        filepath = data_processor.to_excel(crawler.results, filename, data_type)
                    else:
                        filepath = data_processor.to_excel(crawler.results, filename)
                    
                    result["export_file"] = filepath
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"同步执行爬虫失败: {e}")
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/export', methods=['POST'])
def export_data():
    """导出数据"""
    try:
        data = request.get_json()
        crawler_name = data.get('crawler')
        format_type = data.get('format', 'excel')
        
        crawler = crawler_manager.get_crawler(crawler_name)
        if not crawler or not crawler.results:
            return jsonify({"success": False, "error": "没有可导出的数据"})
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{crawler_name}_{timestamp}"
        
        if format_type == 'excel':
            filepath = data_processor.to_excel(crawler.results, filename, crawler_name)
        elif format_type == 'json':
            filepath = data_processor.to_json(crawler.results, filename)
        else:
            return jsonify({"success": False, "error": "不支持的导出格式"})
        
        if filepath:
            return jsonify({
                "success": True,
                "filepath": filepath,
                "filename": os.path.basename(filepath)
            })
        else:
            return jsonify({"success": False, "error": "导出失败"})
            
    except Exception as e:
        logger.error(f"导出数据失败: {e}")
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/files')
def get_files():
    """获取导出文件列表"""
    try:
        files = data_processor.get_export_files()
        return jsonify({"success": True, "files": files})
    except Exception as e:
        logger.error(f"获取文件列表失败: {e}")
        return jsonify({"success": False, "error": str(e)})

@app.route('/api/download/<filename>')
def download_file(filename):
    """下载文件"""
    try:
        from config.settings import EXPORT_DIR
        filepath = EXPORT_DIR / filename
        
        if filepath.exists():
            return send_file(filepath, as_attachment=True)
        else:
            return jsonify({"error": "文件不存在"}), 404
            
    except Exception as e:
        logger.error(f"下载文件失败: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    try:
        status = {
            "crawlers": crawler_manager.get_all_status(),
            "files_count": len(data_processor.get_export_files()),
            "timestamp": datetime.now().isoformat()
        }
        return jsonify({"success": True, "status": status})
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({"success": False, "error": str(e)})

@app.errorhandler(404)
def not_found(error):
    return jsonify({"error": "页面不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"error": "服务器内部错误"}), 500

if __name__ == '__main__':
    init_app()
    app.run(
        host=WEB_CONFIG["host"],
        port=WEB_CONFIG["port"],
        debug=WEB_CONFIG["debug"]
    )
