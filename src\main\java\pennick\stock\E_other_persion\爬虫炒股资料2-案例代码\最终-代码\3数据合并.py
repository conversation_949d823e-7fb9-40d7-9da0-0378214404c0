import os
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter

def process_excel_files():
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    bid_folder = os.path.join(script_dir, '1竞价下载')
    all_folder = os.path.join(script_dir, '2次日下载')

    # 检查文件夹是否存在
    if not os.path.exists(bid_folder) or not os.path.exists(all_folder):
        print("指定的文件夹不存在，请检查。")
        return

    # 第 K 列和第 L 列的索引（openpyxl 中列索引从 1 开始）
    col_k_index = 21
    col_l_index = 22

    # 遍历 1 竞价待处理 文件夹中的所有 Excel 文件
    for filename in os.listdir(bid_folder):
        if filename.endswith('.xlsx'):
            bid_file_path = os.path.join(bid_folder, filename)
            all_file_path = os.path.join(all_folder, filename)

            # 检查 2 全部待处理 文件夹中是否存在同名文件
            if os.path.exists(all_file_path):
                try:
                    # 加载工作簿
                    bid_wb = load_workbook(bid_file_path)
                    bid_sheet = bid_wb.active
                    all_wb = load_workbook(all_file_path)
                    all_sheet = all_wb.active

                    # 查找表头包含指定关键字的列
                    bid_increase_col = None
                    price_change_col = None
                    for col in range(1, all_sheet.max_column + 1):
                        header = all_sheet.cell(row=1, column=col).value
                        if '竞价涨幅' in str(header):
                            bid_increase_col = col
                        if '竞价匹配价' in str(header):
                            price_change_col = col

                    if bid_increase_col and price_change_col:
                        # 检查原表头列数，若不足则添加空列
                        while bid_sheet.max_column < col_l_index:
                            bid_sheet.cell(row=1, column=bid_sheet.max_column + 1, value="")

                        # 设置第 K 列和第 L 列的表头
                        bid_sheet.cell(row=1, column=col_k_index, value='次日竞价涨幅')
                        bid_sheet.cell(row=1, column=col_l_index, value='次日竞价价格')

                        # 查找代码列的索引
                        code_col_index = None
                        for col in range(1, bid_sheet.max_column + 1):
                            if bid_sheet.cell(row=1, column=col).value == '股票代码':
                                code_col_index = col
                                break

                        all_code_col_index = None
                        for col in range(1, all_sheet.max_column + 1):
                            if all_sheet.cell(row=1, column=col).value == 'code':
                                all_code_col_index = col
                                break

                        if code_col_index and all_code_col_index:
                            # 创建匹配字典
                            match_dict = {}
                            for row in range(2, all_sheet.max_row + 1):
                                code = all_sheet.cell(row=row, column=all_code_col_index).value
                                bid_increase = all_sheet.cell(row=row, column=bid_increase_col).value
                                price_change = all_sheet.cell(row=row, column=price_change_col).value
                                match_dict[code] = (bid_increase, price_change)

                            # 遍历 1 竞价待处理 中的数据
                            for row in range(2, bid_sheet.max_row + 1):
                                code = bid_sheet.cell(row=row, column=code_col_index).value
                                if code in match_dict:
                                    bid_sheet.cell(row=row, column=col_k_index, value=match_dict[code][0])
                                    # 保留两位小数后写入第 L 列
                                    price_change_value = match_dict[code][1]
                                    try:
                                        price_change_value = float(price_change_value)
                                        price_change_value = round(price_change_value, 2)
                                        cell = bid_sheet.cell(row=row, column=col_l_index)
                                        cell.value = price_change_value
                                        cell.number_format = '0.00'
                                    except (ValueError, TypeError):
                                        # 如果无法转换为浮点数，保持原值
                                        bid_sheet.cell(row=row, column=col_l_index, value=price_change_value)

                        # 保存修改后的工作簿
                        bid_wb.save(bid_file_path)
                        print(f"处理完成: {filename}")
                    else:
                        print(f"文件 {filename} 中未找到所需的列。")
                except Exception as e:
                    print(f"处理文件 {filename} 时出错: {e}")


if __name__ == "__main__":
    process_excel_files()