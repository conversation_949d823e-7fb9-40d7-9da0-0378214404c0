import requests
from openpyxl import Workbook
from datetime import datetime

url_base = 'https://apphis.longhuvip.com/w1/api/index.php?Order=1&a=HisDaBanList&st=60&c=HisHomeDingPan&PhoneOSNew=1&DeviceID=00000000-296c-20ad-0000-00003eb74e84&VerSion=********&Index=0&Is_st=1&PidType=1&apiv=w31&Type=6&FilterMotherboard=0&Filter=0&FilterTIB=0&Day=2025-02-21&FilterGem=0&'

# 初始化变量以存储所有数据
all_data = []

# 初始化索引值
index = 0

# 循环发送请求，直到获取所有数据为止
while True:
    url = url_base + f'&Index={index}'
    response = requests.get(url).json()
    if 'list' in response:
        data = response['list']
        if len(data) == 0:
            # 如果没有更多数据，退出循环
            break
        else:
            all_data.extend(data)
            # 更新索引值以获取下一页数据
            index += 60
    else:
        print("JSON 数据中不存在 'list' 键")
        break

# 创建一个新的工作簿
wb = Workbook()
# 获取活动工作表
ws = wb.active

# 需要过滤的列索引（从 0 开始）
columns_to_filter = [2, 3, 4, 5, 7, 17, 18, 19, 20, 21, 22, 24, 26, 27, 29, 28, 30, 31, 32]

# 如果有数据，生成默认表头
if all_data:
    num_columns = len(all_data[0])
    headers = [f'Column {i + 1}' for i in range(num_columns) if i not in columns_to_filter]

    # 替换表头名称
    header_replacements = {
        "Column 7": "涨停时间",
        "Column 9": "封单",
        "Column 13": "主力净额",
        "Column 14": "成交额",
        "Column 16": "实际流通",
        "Column 24": "最大封单"
    }
    for i, header in enumerate(headers):
        if header in header_replacements:
            headers[i] = header_replacements[header]

    ws.append(headers)

    # 查找需要转换时间的列索引
    columns_to_convert_time = ['Column 7', 'Column 26']
    conversion_time_indices = []
    for col in columns_to_convert_time:
        new_col = header_replacements.get(col, col)
        try:
            index = headers.index(new_col)
            conversion_time_indices.append(index)
        except ValueError:
            print(f"表头中未找到 '{new_col}'，无法进行时间转换。")

    # 查找需要进行除法运算的列索引及对应的除数
    columns_to_divide = {
        'Column 9': 10000,
        'Column 13': 10000,
        'Column 14': 100000000,
        'Column 24': 10000,
        'Column 16': 100000000
    }
    division_indices = {}
    for col, divisor in columns_to_divide.items():
        new_col = header_replacements.get(col, col)
        try:
            index = headers.index(new_col)
            division_indices[index] = divisor
        except ValueError:
            print(f"表头中未找到 '{new_col}'，无法进行除法运算。")

    # 逐行写入数据
    for item in all_data:
        row = [item[i] for i in range(num_columns) if i not in columns_to_filter]

        # 转换时间
        for idx in conversion_time_indices:
            try:
                timestamp_str = str(row[idx])
                timestamp = int(timestamp_str)
                dt = datetime.fromtimestamp(timestamp)
                row[idx] = dt.strftime('%H:%M')
            except (ValueError, TypeError) as e:
                print(f"转换时间戳时出错，列: {headers[idx]}，数据: {timestamp_str}，错误信息: {e}")

        # 进行除法运算
        for idx, divisor in division_indices.items():
            try:
                value = float(row[idx])
                row[idx] = round(value / divisor, 2)
            except (ValueError, TypeError) as e:
                print(f"进行除法运算时出错，列: {headers[idx]}，数据: {row[idx]}，错误信息: {e}")

        ws.append(row)
else:
    print("all_data 列表为空，没有数据可写入。")

# 保存工作簿到文件
wb.save('output.xlsx')
print("数据已成功保存到 output.xlsx 文件中。")