import requests
import pandas as pd

base_url = 'https://apphq.longhuvip.com/w1/api/index.php?Order=0&a=DailyLimitPerformance&st=2000&apiv=w39&Type=4&c=HomeDingPan&PhoneOSNew=1&DeviceID=d66474b3-fd78-3a95-a56d-76e29e765ea3&VerSion=********&Index=0'
all_info_data = []

# 循环请求不同的 PidType
for pid_type in range(1, 6):
    url = f"{base_url}&PidType={pid_type}"
    try:
        response = requests.get(url).json()
        if 'info' in response:
            info_data = response['info'][0]
            all_info_data.extend(info_data)
    except requests.RequestException as e:
        print(f"请求 PidType={pid_type} 时出错: {e}")
    except KeyError:
        print(f"PidType={pid_type} 的响应中未找到 'info' 键。")

# 将合并后的数据写入 Excel
if all_info_data:
    df = pd.DataFrame(all_info_data)
    try:
        df.to_excel('开盘啦涨停数据.xlsx', index=False)
        print("数据已成功写入 开盘啦涨停数据.xlsx 文件。")
    except Exception as e:
        print(f"写入文件时出错: {e}")
else:
    print("未获取到有效的 info 数据。")
