<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .crawler-card {
            transition: transform 0.2s;
            cursor: pointer;
            height: 200px;
        }
        .crawler-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-ready { background-color: #28a745; }
        .status-running { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        .log-container {
            height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .file-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-graph-up"></i> 股票数据管理平台
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text" id="statusIndicator">
                        <span class="status-indicator status-ready"></span>
                        系统就绪
                    </span>
                </div>
            </div>
        </nav>

        <div class="row">
            <!-- 左侧：爬虫模块 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-robot"></i> 数据采集模块</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="crawlerModules">
                            <!-- 开盘啦数据模块 -->
                            <div class="col-md-6 mb-3">
                                <div class="card crawler-card h-100" onclick="showCrawlerModal('kaipanla')">
                                    <div class="card-body d-flex flex-column">
                                        <h6 class="card-title">
                                            <span class="status-indicator status-ready"></span>
                                            📈 开盘啦数据
                                        </h6>
                                        <p class="card-text flex-grow-1">
                                            涨停、跌停、竞价、炸板等数据<br>
                                            <small class="text-muted">支持4种数据类型</small>
                                        </p>
                                        <div class="mt-auto">
                                            <span class="badge bg-success">自动获取</span>
                                            <span class="badge bg-info">多类型</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 更多模块可以在这里添加 -->
                            <div class="col-md-6 mb-3">
                                <div class="card crawler-card h-100" style="opacity: 0.6;">
                                    <div class="card-body d-flex flex-column">
                                        <h6 class="card-title">
                                            <span class="status-indicator" style="background-color: #6c757d;"></span>
                                            📊 韭研数据
                                        </h6>
                                        <p class="card-text flex-grow-1">
                                            股票异动解析数据<br>
                                            <small class="text-muted">需要完整版本</small>
                                        </p>
                                        <div class="mt-auto">
                                            <span class="badge bg-secondary">开发中</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 执行日志 -->
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-terminal"></i> 执行日志</h5>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLog()">
                            <i class="bi bi-trash"></i> 清空
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="logContainer" class="log-container"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧：文件管理和状态 -->
            <div class="col-md-4">
                <!-- 快速操作 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="bi bi-lightning"></i> 快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="runKaipanlaData()">
                                <i class="bi bi-play-circle"></i> 获取开盘啦数据
                            </button>
                            <button class="btn btn-info" onclick="refreshFiles()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新文件列表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 导出文件 -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-file-earmark-excel"></i> 导出文件</h6>
                    </div>
                    <div class="card-body">
                        <div id="filesList" style="max-height: 400px; overflow-y: auto;">
                            <!-- 文件列表将通过JavaScript动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框：爬虫配置 -->
    <div class="modal fade" id="crawlerModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">爬虫配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 动态内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeCrawler()">执行</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentCrawler = null;
        let crawlerModal = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            crawlerModal = new bootstrap.Modal(document.getElementById('crawlerModal'));
            refreshFiles();
            addLog('股票数据管理平台已启动', 'success');
        });

        // 显示爬虫配置模态框
        function showCrawlerModal(crawlerName) {
            currentCrawler = crawlerName;
            
            document.getElementById('modalTitle').textContent = '开盘啦数据 - 配置';
            
            let modalContent = `
                <div class="mb-3">
                    <label class="form-label">日期</label>
                    <input type="date" class="form-control" id="crawlerDate" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="mb-3">
                    <label class="form-label">数据类型</label>
                    <select class="form-select" id="dataType">
                        <option value="涨停">涨停</option>
                        <option value="跌停">跌停</option>
                        <option value="竞价">竞价</option>
                        <option value="炸板">炸板</option>
                    </select>
                </div>
            `;

            document.getElementById('modalBody').innerHTML = modalContent;
            crawlerModal.show();
        }

        // 执行爬虫
        async function executeCrawler() {
            if (!currentCrawler) return;

            const params = {
                date: document.getElementById('crawlerDate').value,
                data_type: document.getElementById('dataType').value
            };

            crawlerModal.hide();
            
            addLog(`开始执行 ${currentCrawler} 爬虫...`, 'info');
            updateStatus('running');

            try {
                const response = await axios.post('/api/crawl/sync', {
                    crawler: currentCrawler,
                    params: params
                });

                if (response.data.success) {
                    addLog(`${currentCrawler} 执行成功，获取 ${response.data.data_count} 条数据`, 'success');
                    if (response.data.export_file) {
                        addLog(`文件已导出: ${response.data.export_file}`, 'info');
                    }
                    refreshFiles();
                } else {
                    addLog(`${currentCrawler} 执行失败: ${response.data.error}`, 'error');
                }
            } catch (error) {
                addLog(`${currentCrawler} 执行出错: ${error.message}`, 'error');
            } finally {
                updateStatus('ready');
            }
        }

        // 快速获取开盘啦数据
        async function runKaipanlaData() {
            addLog('开始获取开盘啦数据...', 'info');
            updateStatus('running');

            const date = new Date().toISOString().split('T')[0];
            const params = { date: date, data_type: '涨停' };

            try {
                const response = await axios.post('/api/crawl/sync', {
                    crawler: 'kaipanla',
                    params: params
                });

                if (response.data.success) {
                    addLog(`获取成功，共 ${response.data.data_count} 条数据`, 'success');
                    refreshFiles();
                } else {
                    addLog(`获取失败: ${response.data.error}`, 'error');
                }
            } catch (error) {
                addLog(`获取出错: ${error.message}`, 'error');
            } finally {
                updateStatus('ready');
            }
        }

        // 刷新文件列表
        async function refreshFiles() {
            try {
                const response = await axios.get('/api/files');
                if (response.data.success) {
                    renderFiles(response.data.files);
                }
            } catch (error) {
                addLog('刷新文件列表失败: ' + error.message, 'error');
            }
        }

        // 渲染文件列表
        function renderFiles(files) {
            const container = document.getElementById('filesList');
            
            if (files.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">暂无导出文件</p>';
                return;
            }

            container.innerHTML = files.map(file => `
                <div class="file-item d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-bold">${file.name}</div>
                        <small class="text-muted">${formatFileSize(file.size)} | ${formatDate(file.modified_time)}</small>
                    </div>
                    <a href="/api/download/${file.name}" class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-download"></i>
                    </a>
                </div>
            `).join('');
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const container = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            
            const logEntry = document.createElement('div');
            logEntry.className = logClass;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 更新状态
        function updateStatus(status = null) {
            const indicator = document.querySelector('#statusIndicator');
            const statusDot = indicator.querySelector('.status-indicator');
            
            if (status === 'running') {
                statusDot.className = 'status-indicator status-running';
                indicator.innerHTML = '<span class="status-indicator status-running"></span>执行中...';
            } else {
                statusDot.className = 'status-indicator status-ready';
                indicator.innerHTML = '<span class="status-indicator status-ready"></span>系统就绪';
            }
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        }
    </script>
</body>
</html>
