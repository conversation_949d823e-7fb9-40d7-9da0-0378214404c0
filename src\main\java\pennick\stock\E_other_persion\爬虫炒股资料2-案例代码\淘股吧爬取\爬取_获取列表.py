from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time
import atexit
import psutil
import os
import signal

# 配置项
TARGET_URL = "https://www.tgb.cn/user/blog/moreTopic?pageNum=2&pageNo=1&sortFlag=T&userID=6532339"
LOGIN_URL = "https://www.tgb.cn/a/2euEg8VWnp6"

def kill_chrome_processes():
    """结束所有Chrome相关进程"""
    try:
        print("正在清理Chrome进程...")
        for proc in psutil.process_iter(['pid', 'name']):
            # 查找Chrome相关进程
            if any(chrome_process in proc.info['name'].lower() 
                  for chrome_process in ['chrome', 'chromedriver']):
                try:
                    print(f"正在结束进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    os.kill(proc.info['pid'], signal.SIGTERM)
                except Exception as e:
                    print(f"结束进程失败: {str(e)}")
                    continue
        print("Chrome进程清理完成")
    except Exception as e:
        print(f"清理Chrome进程时出错: {str(e)}")

def wait_and_find_element(driver, by, value, timeout=10):
    """等待并查找元素"""
    try:
        element = driver.find_element(by, value)
        print(f"成功找到元素: {by}={value}")
        return element
    except Exception as e:
        print(f"未找到元素: {by}={value}, 错误信息: {str(e)}")
        return None

def scroll_to_bottom(driver, step=400, delay=0.01):
    """模拟渐进式滚动到页面底部"""
    print("开始模拟渐进式滚动到页面底部...")
    
    # 获取初始滚动高度
    total_height = driver.execute_script("return document.body.scrollHeight")
    current_position = 0
    
    while current_position < total_height:
        # 计算下一个滚动位置
        current_position += step
        
        # 滚动到新位置
        driver.execute_script(f"window.scrollTo(0, {current_position});")
        print(f"滚动到位置: {current_position}/{total_height}")
        
        # 等待内容加载
        time.sleep(delay)
        
        # 重新获取页面高度（可能因为图片加载而变化）
        new_height = driver.execute_script("return document.body.scrollHeight")
        if new_height > total_height:
            total_height = new_height
            print(f"页面高度更新为: {total_height}")
    
    # 最后确保滚动到底部
    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    print("已滚动到页面底部")

def open_page(initial_url=TARGET_URL):
    """
    打开页面并获取所有文章链接
    :param initial_url: 初始访问链接，包含页码信息
    """
    driver = None
    try:
        # 配置Chrome选项
        print("正在配置Chrome选项...")
        chrome_options = Options()
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-background-networking')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        # 添加启动时最大化窗口的选项
        chrome_options.add_argument('--start-maximized')
        
        # 添加User-Agent
        chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')
        
        # 添加实验性选项
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 创建Chrome驱动
        print("正在创建Chrome驱动...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 确保窗口最大化
        driver.maximize_window()
        print("浏览器窗口已最大化")
        
        # 设置页面加载超时时间为2秒
        driver.set_page_load_timeout(3)
        
        # 打开网页
        print(f"正在打开页面: {LOGIN_URL}")
        try:
            driver.get(LOGIN_URL)
        except TimeoutException:
            print("页面加载超时，继续执行...")
            pass
        
        # 等待页面基本加载
        print("等待页面基本加载 2 秒...")
        time.sleep(1)
        
        # 查找并点击登录/注册按钮
        print("正在查找登录/注册按钮...")
        # 尝试多种定位方式
        login_btn = wait_and_find_element(driver, By.CSS_SELECTOR, "a.header-a[onclick='loginPanel()']")
        if not login_btn:
            login_btn = wait_and_find_element(driver, By.XPATH, "//a[@class='header-a' and @onclick='loginPanel()']")
        
        if login_btn:
            print("找到登录/注册按钮,准备点击...")
            # 使用JavaScript点击以避免可能的遮挡问题
            driver.execute_script("arguments[0].click();", login_btn)
            print("已点击登录/注册按钮")
            time.sleep(1)
            # 等待账号登录按钮出现
            print("正在查找账号登录按钮...")
            account_login_btn = wait_and_find_element(driver, By.ID, "userLoginBtn")
            if account_login_btn:
                print("找到账号登录按钮,准备点击...")
                account_login_btn.click()
                print("已点击账号登录按钮")
                
                # 输入用户名
                print("正在查找用户名输入框...")
                username_input = wait_and_find_element(driver, By.ID, "userPanelName")
                if username_input:
                    print("找到用户名输入框,准备输入...")
                    username_input.clear()
                    username_input.send_keys("spenser")
                    print("已输入用户名")
                    time.sleep(1)
                else:
                    print("未找到用户名输入框")
                
                # 输入密码
                print("正在查找密码输入框...")
                password_input = wait_and_find_element(driver, By.ID, "userPanelPwd") 
                if password_input:
                    print("找到密码输入框,准备输入...")
                    password_input.clear()
                    password_input.send_keys("eight986")
                    print("已输入密码")
                    time.sleep(1)
                else:
                    print("未找到密码输入框")
                
                # 点击登录按钮
                print("正在查找登录提交按钮...")
                login_button = wait_and_find_element(driver, By.ID, "loginBtn")
                if login_button:
                    print("找到登录提交按钮,准备点击...")
                    login_button.click()
                    print("已点击登录提交按钮")
                    time.sleep(2)
                    
                    # 登录成功后访问新链接
                    print(f"正在访问初始页面: {TARGET_URL}")
                    
                    try:
                        # 先访问第一页获取总页数
                        driver.get(TARGET_URL)
                        print("成功加载第一页")
                        time.sleep(2)
                        
                        # 从URL中获取总页数和用户ID
                        current_url = driver.current_url
                        total_pages = int(current_url.split("pageNum=")[1].split("&")[0])
                        user_id = current_url.split("userID=")[1]
                        base_url = "https://www.tgb.cn/user/blog/moreTopic"
                        print(f"总页数: {total_pages}")
                        
                        # 创建文件准备写入
                        with open(f'article_links_{user_id}.txt', 'w', encoding='utf-8') as f:
                            # 遍历每一页，从1到total_pages
                            for page in range(1, total_pages + 1):
                                if page > 1:
                                    # 使用原始URL中的total_pages作为pageNum
                                    page_url = f"{base_url}?pageNum={total_pages}&pageNo={page}&sortFlag=T&userID={user_id}"
                                    print(f"正在访问第 {page}/{total_pages} 页: {page_url}")
                                    driver.get(page_url)
                                    time.sleep(2)
                                
                                # 获取当前页面的所有文章链接
                                articles = driver.find_elements(By.CSS_SELECTOR, "td.suh a[href^='a/']")
                                
                                # 保存当前页的链接
                                for article in articles:
                                    href = article.get_attribute('href')
                                    f.write(f"{href}\n")
                                    print(f"已保存文章链接: {href}")
                                
                                print(f"第 {page}/{total_pages} 页的链接已保存")
                        
                        print(f"所有 {total_pages} 页的文章链接已保存到 article_links_{user_id}.txt")
                        
                    except Exception as e:
                        print(f"访问页面或获取内容时发生错误: {str(e)}")
                    
                    return  # 直接返回,触发finally中的清理代码
                else:
                    print("未找到登录提交按钮")
            else:
                print("未找到账号登录按钮")
        else:
            print("未找到登录/注册按钮")
            
        # 如果登录失败,等待10秒后退出
        time.sleep(10)
        
        # 保持窗口打开
        time.sleep(1)
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
    
    finally:
        try:
            if driver:
                print("正在关闭浏览器...")
                # 关闭所有窗口
                driver.quit()
                print("浏览器已关闭")
                # 确保进程被终止
                kill_chrome_processes()
        except Exception as e:
            print(f"关闭浏览器时出错: {str(e)}")
            # 如果正常关闭失败，强制结束进程
            kill_chrome_processes()

if __name__ == '__main__':
    # 注册程序退出时的清理函数
    atexit.register(kill_chrome_processes)
    
    try:
        open_page()  # 直接使用配置的TARGET_URL
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        # 确保在程序结束时清理所有Chrome进程
        kill_chrome_processes()
