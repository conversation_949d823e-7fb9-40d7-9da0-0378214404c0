import requests
from openpyxl import Workbook
import datetime

url_base = 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=4&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day=2025-02-21&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&'

# 初始化变量以存储所有数据
all_data = []

# 初始化索引值
index = 0

# 循环发送请求，直到获取所有数据为止
while True:
    url = url_base + f'&Index={index}'
    response = requests.get(url).json()
    if 'list' in response:
        data = response['list']
        if len(data) == 0:
            # 如果没有更多数据，退出循环
            break
        else:
            all_data.extend(data)
            # 更新索引值以获取下一页数据
            index += 60
    else:
        print("JSON 数据中不存在 'list' 键")
        break

# 列名列表，用于删除指定的列
columns_to_delete = [
    "Column 3", "Column 4", "Column 5", "Column 6", "Column 8", "Column 24", "Column 25", "Column 26", "Column 27", "Column 28", "Column 29", "Column 30",
    "Column 18", "Column 19", "Column 20", "Column 21", "Column 22", "Column 23",
    "Column 31", "Column 32", "Column 33"
]

# 创建一个新的工作簿
wb = Workbook()
# 获取活动工作表
ws = wb.active

# 如果有数据，生成默认表头并写入数据
if all_data:
    num_columns = len(all_data[0])
    headers = [f'Column {i+1}' for i in range(num_columns)]

    # 确定要保留的列的索引
    columns_to_keep_indices = []
    deleted_indices = []
    for i, header in enumerate(headers):
        if header not in columns_to_delete:
            columns_to_keep_indices.append(i)
        else:
            deleted_indices.append(i)

    print(f"删除的列的索引 (从0开始计数): {deleted_indices}")

    # 创建新的表头，只包含要保留的列
    filtered_headers = [headers[i] for i in columns_to_keep_indices]

    # 替换指定的表头名称
    header_name_mapping = {
        "Column 7": "涨停时间",
        "Column 9": "封单",
        "Column 13": "主力净额",
        "Column 14": "成交金额",
        "Column 15": "实际换手",
        "Column 16": "实际流通"
    }
    for i, header in enumerate(filtered_headers):
        if header in header_name_mapping:
            filtered_headers[i] = header_name_mapping[header]

    ws.append(filtered_headers)

    # 查找需要修改值的列在原始表头中的索引
    column_9_index_original = -1
    column_13_index_original = -1
    column_14_index_original = -1
    column_16_index_original = -1
    column_7_index_original = -1 # 新增 Column 7 的索引

    for i, header in enumerate(headers):
        if header == "Column 9":
            column_9_index_original = i
        elif header == "Column 13":
            column_13_index_original = i
        elif header == "Column 14":
            column_14_index_original = i
        elif header == "Column 16":
            column_16_index_original = i
        elif header == "Column 7": # 查找 Column 7 的索引
            column_7_index_original = i

    # 逐行写入数据，只包含要保留的列
    for item in all_data:
        filtered_item = [item[i] for i in columns_to_keep_indices]

        # 修改 Column 9 的值
        if column_9_index_original != -1 and column_9_index_original in columns_to_keep_indices:
            column_9_index_in_kept = columns_to_keep_indices.index(column_9_index_original)
            if column_9_index_in_kept < len(filtered_item):
                try:
                    value = float(filtered_item[column_9_index_in_kept])
                    modified_value = round(value / 10000, 2)
                    filtered_item[column_9_index_in_kept] = modified_value
                except (ValueError, TypeError):
                    pass # 如果值不能转换为 float，则保持原样

        # 修改 Column 13 的值
        if column_13_index_original != -1 and column_13_index_original in columns_to_keep_indices:
            column_13_index_in_kept = columns_to_keep_indices.index(column_13_index_original)
            if column_13_index_in_kept < len(filtered_item):
                try:
                    value = float(filtered_item[column_13_index_in_kept])
                    modified_value = round(value / 10000, 2)
                    filtered_item[column_13_index_in_kept] = modified_value
                except (ValueError, TypeError):
                    pass # 如果值不能转换为 float，则保持原样

        # 修改 Column 14 的值
        if column_14_index_original != -1 and column_14_index_original in columns_to_keep_indices:
            column_14_index_in_kept = columns_to_keep_indices.index(column_14_index_original)
            if column_14_index_in_kept < len(filtered_item):
                try:
                    value = float(filtered_item[column_14_index_in_kept])
                    modified_value = round(value / 100000000, 2)
                    filtered_item[column_14_index_in_kept] = modified_value
                except (ValueError, TypeError):
                    pass # 如果值不能转换为 float，则保持原样

        # 修改 Column 15 的值，假设不需要修改，保持原样，如果需要修改，请添加类似 Column 14 的代码块

        # 修改 Column 16 的值
        if column_16_index_original != -1 and column_16_index_original in columns_to_keep_indices:
            column_16_index_in_kept = columns_to_keep_indices.index(column_16_index_original)
            if column_16_index_in_kept < len(filtered_item):
                try:
                    value = float(filtered_item[column_16_index_in_kept])
                    modified_value = round(value / 100000000, 2)
                    filtered_item[column_16_index_in_kept] = modified_value
                except (ValueError, TypeError):
                    pass # 如果值不能转换为 float，则保持原样

        # 修改 Column 7 的值，转换为时分
        if column_7_index_original != -1 and column_7_index_original in columns_to_keep_indices:
            column_7_index_in_kept = columns_to_keep_indices.index(column_7_index_original)
            if column_7_index_in_kept < len(filtered_item):
                try:
                    timestamp = int(filtered_item[column_7_index_in_kept])
                    # 假设时间戳是秒级
                    datetime_object = datetime.datetime.fromtimestamp(timestamp)
                    formatted_time = datetime_object.strftime('%H:%M')
                    filtered_item[column_7_index_in_kept] = formatted_time
                except (ValueError, TypeError):
                    pass # 如果值不能转换为 int 或时间戳转换失败，则保持原样


        ws.append(filtered_item)
else:
    print("all_data 列表为空，没有数据可写入。")

# 保存工作簿到文件
wb.save('output.xlsx')
print("数据已成功保存到 output.xlsx 文件中。")