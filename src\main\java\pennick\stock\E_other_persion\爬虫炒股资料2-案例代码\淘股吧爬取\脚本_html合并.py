import os

# 获取当前脚本所在的目录
script_dir = os.path.dirname(__file__)

# 设置输出文件的路径
output_file_path = os.path.join(script_dir, "合并文件2019.html")

# 设置文件夹路径
folder_path = r"D:\01\其他项目\临时归档\定制-爬取测试tgb\ruobign"

# 获取文件夹中所有HTML文件的文件名
html_files = [f for f in os.listdir(folder_path) if f.endswith(".html")]

# 初始化一个空字符串来保存合并后的HTML内容
merged_html_content = ""

# 遍历每个HTML文件，读取内容并追加到合并后的字符串中
for i, html_file in enumerate(html_files):
    file_path = os.path.join(folder_path, html_file)
    with open(file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()

        # 在每两篇HTML之间添加横线和两行空行
        if i > 0:
            merged_html_content += '<hr/>\n\n'

        merged_html_content += html_content

# 将合并后的HTML内容写入新文件
with open(output_file_path, 'w', encoding='utf-8') as output_file:
    output_file.write(merged_html_content)

print(f"HTML文件已成功合并到 {output_file_path}")