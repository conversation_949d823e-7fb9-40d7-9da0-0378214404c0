import win32api
import win32gui
import win32process
import win32con
import psutil
import ctypes
from ctypes import *
import keyboard
import tkinter as tk
from tkinter import messagebox
import time
import os
import pandas as pd
import glob
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 同花顺通信相关常量和函数
FAGE_READWRITE = 0x04
PROCESS_ALL_ACCESS = 0x001F0FFF
VIRTUAL_MEN = (0x1000 | 0x2000)

kernel32 = windll.kernel32
user32 = windll.user32
IsWindowVisible = user32.IsWindowVisible


def get_pids(pname):
    pids = []
    for proc in psutil.process_iter():
        if pname in proc.name():
            pids.append(proc.pid)
    return pids


def get_handles(pid):
    def callback(hwnd, handles):
        if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
            _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
            if found_pid == pid:
                handles.append(hwnd)
        return True

    handles = []
    win32gui.EnumWindows(callback, handles)
    return handles


def get_handle(pname):
    pids = get_pids(pname)
    for pid in pids:
        handles = get_handles(pid)
        for hwnd in handles:
            if IsWindowVisible(hwnd):
                return hwnd
    return None


def find_dialog_window():
    """查找同花顺弹出的对话框窗口"""
    dialog_hwnd = None
    
    def enum_windows_callback(hwnd, _):
        nonlocal dialog_hwnd
        if win32gui.IsWindowVisible(hwnd):
            window_text = win32gui.GetWindowText(hwnd)
            # 根据对话框的标题来识别，可能需要调整
            if "同花顺" in window_text and len(window_text) < 30:
                dialog_hwnd = hwnd
                return False
        return True
    
    win32gui.EnumWindows(enum_windows_callback, None)
    return dialog_hwnd


def click_at_position(hwnd, x_offset=0, y_offset=0, from_center=False):
    """在窗口的指定坐标位置点击
    
    Args:
        hwnd: 窗口句柄
        x_offset: 横向偏移量
        y_offset: 纵向偏移量
        from_center: 是否从窗口中心点计算偏移
    """
    # 获取窗口的客户区域左上角在屏幕上的坐标和尺寸
    left, top, right, bottom = win32gui.GetWindowRect(hwnd)
    width = right - left
    height = bottom - top
    
    if from_center:
        # 计算窗口中心点
        center_x = left + width // 2
        center_y = top + height // 2
        
        # 从中心点计算偏移
        screen_x = center_x + x_offset
        screen_y = center_y + y_offset
    else:
        # 从左上角计算偏移
        screen_x = left + x_offset
        screen_y = top + y_offset
    
    # 将窗口设置为前台窗口
    win32gui.SetForegroundWindow(hwnd)
    
    # 点击指定位置
    win32api.SetCursorPos((screen_x, screen_y))
    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
    time.sleep(0.05)
    win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
    
    return screen_x, screen_y, width, height


def get_first_excel_data():
    """获取当前目录下Excel数据"""
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 查找所有Excel文件
        excel_files = glob.glob(os.path.join(current_dir, "*.xlsx")) + glob.glob(os.path.join(current_dir, "*.xls"))
        
        if not excel_files:
            raise Exception("当前目录下未找到Excel文件")
            
        # 获取第一个Excel文件
        first_excel = excel_files[0]
        print(f"读取Excel文件：{first_excel}")
        
        # 读取Excel文件
        df = pd.read_excel(first_excel)
        
        # 获取第5列数据（不包含表头）
        if len(df.columns) < 5:
            raise Exception("Excel文件列数少于5列")
            
        # 确保所有值都转换为字符串
        values = df.iloc[:, 5].fillna('').astype(str).tolist()  # 第5列索引为4
        # 过滤掉空字符串
        values = [v for v in values if v.strip()]
        
        if not values:
            raise Exception("第5列没有数据")
            
        print(f"共读取到{len(values)}条数据")
        return values
        
    except Exception as e:
        raise Exception(f"读取Excel文件失败：{str(e)}")


def send_ctrl_i_to_ths(remark_content):
    try:
        # 查找同花顺主窗口
        ths_handle = get_handle('hexin.exe')
        if not ths_handle:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", "未找到同花顺窗口，请确保同花顺已经打开")
            root.destroy()
            return
        
        # 激活同花顺窗口
        win32gui.SetForegroundWindow(ths_handle)
        time.sleep(0.2)
        
        # 发送 Ctrl + I 快捷键
        keyboard.press('ctrl')
        keyboard.press('i')
        keyboard.release('i')
        keyboard.release('ctrl')
        
        # 等待弹出窗口出现
        time.sleep(1.5)
        
        # 查找弹出的对话框窗口
        dialog_hwnd = find_dialog_window()
        
        # 如果找到对话框窗口，清空输入框并输入备注内容
        if dialog_hwnd:
            # 设为活动窗口
            win32gui.SetForegroundWindow(dialog_hwnd)
            time.sleep(0.5)  # 等待窗口激活
            
            # 先点击窗口中心
            click_at_position(dialog_hwnd, x_offset=0, y_offset=0, from_center=True)
            time.sleep(0.3)  # 等待点击生效
            
            # 先选中全部内容 (Ctrl+A)
            keyboard.press('ctrl')
            keyboard.press('a')
            keyboard.release('a')
            keyboard.release('ctrl')
            
            # 等待片刻
            time.sleep(0.2)
            
            # 删除选中内容
            keyboard.press('backspace')
            keyboard.release('backspace')
            
            # 等待片刻
            time.sleep(0.2)
            
            # 输入备注内容
            keyboard.write(remark_content)
            
            # 等待输入完成
            time.sleep(0.5)
            
            # # 按下Shift+Tab切换到上一个输入框
            # keyboard.press('shift')
            # keyboard.press('tab')
            # keyboard.release('tab')
            # keyboard.release('shift')
            #
            # # 等待切换完成
            # time.sleep(0.3)
            #
            # # 再次选中全部内容 (Ctrl+A)
            # keyboard.press('ctrl')
            # keyboard.press('a')
            # keyboard.release('a')
            # keyboard.release('ctrl')
            #
            # # 等待片刻
            # time.sleep(0.2)
            #
            # # 删除选中内容
            # keyboard.press('backspace')
            # keyboard.release('backspace')
            #
            # # 等待片刻
            # time.sleep(0.2)
            #
            # # 输入相同的备注内容
            # keyboard.write(remark_content)
            #
            # # 等待输入完成
            # time.sleep(0.5)
            
            # 要点击的偏移量
            x_offset = 65
            y_offset = 105
            
            # 在对话框中点击中心点偏移位置
            screen_x, screen_y, width, height = click_at_position(dialog_hwnd, x_offset, y_offset, from_center=True)
            print(f"点击对话框窗口: 窗口尺寸({width}x{height})")
            print(f"窗口中心点偏移: 向右{x_offset}px, 向下{y_offset}px")
            print(f"实际点击屏幕坐标: ({screen_x}, {screen_y})")
        else:
            print("未找到对话框窗口，尝试在主窗口操作")
            # 如果找不到对话框，尝试在主窗口中执行操作
            
            # 先点击窗口中心
            click_at_position(ths_handle, x_offset=0, y_offset=0, from_center=True)
            time.sleep(0.3)  # 等待点击生效
            
            # 先选中全部内容 (Ctrl+A)
            keyboard.press('ctrl')
            keyboard.press('a')
            keyboard.release('a')
            keyboard.release('ctrl')
            
            # 等待片刻
            time.sleep(0.2)
            
            # 删除选中内容
            keyboard.press('backspace')
            keyboard.release('backspace')
            
            # 等待片刻
            time.sleep(0.2)
            
            # 输入备注内容
            keyboard.write(remark_content)
            
            # 等待输入完成
            time.sleep(0.5)
            
            # 按下Shift+Tab切换到上一个输入框
            keyboard.press('shift')
            keyboard.press('tab')
            keyboard.release('tab')
            keyboard.release('shift')
            
            # 等待切换完成
            time.sleep(0.3)
            
            # 再次选中全部内容 (Ctrl+A)
            keyboard.press('ctrl')
            keyboard.press('a')
            keyboard.release('a')
            keyboard.release('ctrl')
            
            # 等待片刻
            time.sleep(0.1)
            
            # 删除选中内容
            keyboard.press('backspace')
            keyboard.release('backspace')
            
            # 等待片刻
            time.sleep(0.1)
            
            # 输入相同的备注内容
            keyboard.write(remark_content)
            
            # 等待输入完成
            time.sleep(0.5)
            
            # 要点击的偏移量
            x_offset = 65
            y_offset = 105
            
            # 在主窗口中点击中心点偏移位置
            screen_x, screen_y, width, height = click_at_position(ths_handle, x_offset, y_offset, from_center=True)
            print(f"点击主窗口: 窗口尺寸({width}x{height})")
            print(f"窗口中心点偏移: 向右{x_offset}px, 向下{y_offset}px")
            print(f"实际点击屏幕坐标: ({screen_x}, {screen_y})")

    except Exception as e:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"操作同花顺失败\n{e}")
        root.destroy()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle('同花顺自动标记工具')
        self.setFixedSize(400, 200)
        
        # 创建中心部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设置窗口背景色为白色
        self.setStyleSheet("background-color: white;")
        
        # 创建标题标签
        title_label = QLabel('同花顺 自动标记 涨停原因')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: red;")
        title_label.setFont(QFont('Microsoft YaHei', 16, QFont.Bold))
        
        # 创建按钮
        self.start_button = QPushButton('开始批量标记')
        self.start_button.setFont(QFont('Microsoft YaHei', 14, QFont.Bold))  # 设置粗体
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: black;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                min-width: 150px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #333333;
            }
        """)
        self.start_button.clicked.connect(self.start_marking)
        
        # 添加部件到布局
        layout.addStretch()
        layout.addWidget(title_label)
        layout.addStretch()
        layout.addWidget(self.start_button, alignment=Qt.AlignCenter)
        layout.addStretch()
        
    def start_marking(self):
        try:
            # 更改按钮文字
            self.start_button.setText('标记中')
            self.start_button.setEnabled(False)  # 禁用按钮
            QApplication.processEvents()  # 确保界面更新
            
            # 获取Excel数据
            remark_contents = get_first_excel_data()
            total_count = len(remark_contents)
            
            for i in range(total_count):
                print(f"执行第 {i+1}/{total_count} 次操作")
                print(f"当前备注内容：{remark_contents[i]}")
                
                # 确保窗口激活
                ths_handle = get_handle('hexin.exe')
                if not ths_handle:
                    messagebox.showerror("错误", "未找到同花顺窗口，请确保同花顺已经打开")
                    break
                    
                # 强制激活窗口并等待
                win32gui.SetForegroundWindow(ths_handle)
                time.sleep(1)  # 增加等待时间确保窗口真正激活
                
                send_ctrl_i_to_ths(remark_contents[i])
                
                # 等待操作完成
                time.sleep(0.5)  # 增加等待时间
                
                # 按下向下键
                keyboard.press('down')
                time.sleep(0.1)  # 添加按键间隔
                keyboard.release('down')
                
                # 等待界面响应
                time.sleep(0.1)  # 增加等待时间
                
                # 在循环之间添加更长的间隔
                time.sleep(1.5)
                
            # 操作完成后恢复按钮
            self.start_button.setText('开始批量标记')
            self.start_button.setEnabled(True)
                
        except Exception as e:
            # 发生错误时恢复按钮
            self.start_button.setText('开始批量标记')
            self.start_button.setEnabled(True)
            messagebox.showerror("错误", f"程序执行失败\n{e}")


if __name__ == "__main__":
    app = QApplication([])
    window = MainWindow()
    window.show()
    app.exec_()