from flask import Flask, render_template, jsonify, request
from mootdx.quotes import Quotes
import pandas as pd
import requests

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/stock_data')
def get_stock_data():
    date = request.args.get('date')
    symbol = request.args.get('symbol')
    
    if not date or not symbol:
        return jsonify({'error': '请提供日期和股票代码'}), 400
    
    client = Quotes.factory(market='std')
    
    # 获取两段数据
    data1 = client.transactions(symbol=symbol, start=2000, offset=2000, date=date)
    data2 = client.transactions(symbol=symbol, start=0, offset=2000, date=date)
    
    # 合并数据
    combined_data = pd.concat([data1, data2], ignore_index=True)
    
    # 转换数据格式为前端需要的格式
    result = {
        'time': combined_data['time'].tolist(),
        'price': combined_data['price'].tolist(),
        'vol': combined_data['vol'].tolist(),
        'buyorsell': combined_data['buyorsell'].tolist()
    }
    
    return jsonify(result)

@app.route('/api/bid_data')
def get_bid_data():
    date = request.args.get('date')
    symbol = request.args.get('symbol')
    
    if not date or not symbol:
        return jsonify({'error': '请提供日期和股票代码'}), 400

    # 构建API请求URL
    url = f'https://apphis.longhuvip.com/w1/api/index.php?Day={date}&PhoneOSNew=2&StockID={symbol}&Token=71970ad0139ddc692f9f0ccd4bbe415e&UserID=1708828&VerSion=********&a=GetStockBid&apiv=w39&c=StockL2History'
    
    try:
        response = requests.get(url)
        data = response.json()
        
        return jsonify({
            'bid': data.get('bid', []),
            'preclose_px': data.get('preclose_px'),
            'hprice': data.get('hprice'),
            'lprice': data.get('lprice'),
            'openpx': data.get('openpx')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(port=5046, debug=True)