#!/usr/bin/env python3
"""
股票数据平台演示脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_platform():
    """演示平台功能"""
    print("🚀 股票数据管理平台演示")
    print("=" * 60)
    
    # 1. 初始化配置
    print("\n📁 1. 初始化项目结构...")
    try:
        from config.settings import create_directories, DATA_SOURCES
        create_directories()
        print("✅ 项目目录创建完成")
        
        print(f"\n📊 支持的数据源 ({len(DATA_SOURCES)}个):")
        for key, config in DATA_SOURCES.items():
            print(f"   • {config['name']}: {config['description']}")
            
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    # 2. 演示数据处理功能
    print("\n🔧 2. 演示数据处理功能...")
    try:
        # 模拟股票数据
        sample_data = [
            {"股票代码": "sh600000", "股票名称": "浦发银行", "涨跌幅": "3.45%", "成交额": 1234567890},
            {"股票代码": "sz000001", "股票名称": "平安银行", "涨跌幅": "2.10%", "成交额": 987654321},
            {"股票代码": "bj430047", "股票名称": "诺思兰德", "涨跌幅": "5.67%", "成交额": 456789123}
        ]
        
        print(f"✅ 模拟数据生成完成 ({len(sample_data)}条)")
        
        # 演示数据清理
        print("\n🧹 数据清理演示:")
        for item in sample_data:
            original_code = item["股票代码"]
            # 简单的代码清理逻辑
            cleaned_code = original_code.replace("sh", "").replace("sz", "").replace("bj", "")
            print(f"   {original_code} -> {cleaned_code}")
            
    except Exception as e:
        print(f"❌ 数据处理演示失败: {e}")
        return False
    
    # 3. 演示文件导出功能
    print("\n💾 3. 演示文件导出功能...")
    try:
        import json
        from datetime import datetime
        
        # 导出JSON文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"demo_data_{timestamp}.json"
        
        # 获取导出目录
        from config.settings import EXPORT_DIR
        json_filepath = EXPORT_DIR / json_filename
        
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
            
        print(f"✅ JSON文件导出成功: {json_filepath}")
        
        # 检查文件大小
        file_size = json_filepath.stat().st_size
        print(f"   文件大小: {file_size} 字节")
        
    except Exception as e:
        print(f"❌ 文件导出演示失败: {e}")
        return False
    
    # 4. 显示平台功能概览
    print("\n🎯 4. 平台功能概览:")
    features = [
        "✅ 统一数据采集框架",
        "✅ 多数据源支持 (韭研、开盘啦、龙虎榜)",
        "✅ 自动数据处理和清洗",
        "✅ 多格式数据导出 (Excel、JSON)",
        "✅ Web管理界面",
        "✅ 一键执行功能",
        "✅ 实时日志监控",
        "✅ 文件管理系统"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    # 5. 使用说明
    print("\n📖 5. 使用说明:")
    print("   1️⃣  运行 'python start.py' 启动Web界面")
    print("   2️⃣  访问 http://127.0.0.1:8000 打开管理界面")
    print("   3️⃣  点击不同爬虫模块进行配置")
    print("   4️⃣  执行爬虫获取数据")
    print("   5️⃣  在右侧查看和下载导出文件")
    print("   6️⃣  使用'一键获取所有数据'快速获取全部数据")
    
    # 6. 项目结构展示
    print("\n📂 6. 项目结构:")
    structure = """
    stock_platform/
    ├── config/          # 配置文件
    ├── core/            # 核心模块 (爬虫基类、数据处理)
    ├── crawlers/        # 爬虫模块 (韭研、开盘啦、龙虎榜)
    ├── web/             # Web界面 (Flask应用、HTML模板)
    ├── data/            # 数据存储
    │   ├── raw/         # 原始数据
    │   ├── processed/   # 处理后数据
    │   └── exports/     # 导出文件
    └── logs/            # 日志文件
    """
    print(structure)
    
    print("\n🎉 演示完成！")
    print("=" * 60)
    print("💡 提示: 这是一个完整的股票数据管理平台，整合了您原有的")
    print("   分散功能，提供统一的操作界面和管理方式。")
    print("\n🚀 准备启动平台? 运行: python start.py")
    
    return True

def show_quick_start():
    """显示快速开始指南"""
    print("\n" + "="*60)
    print("🚀 快速开始指南")
    print("="*60)
    
    steps = [
        ("1. 安装依赖", "pip install flask requests beautifulsoup4 pandas openpyxl"),
        ("2. 启动平台", "python start.py"),
        ("3. 打开浏览器", "访问 http://127.0.0.1:8000"),
        ("4. 选择爬虫", "点击韭研数据、开盘啦数据或龙虎榜数据"),
        ("5. 配置参数", "设置日期、数据类型等参数"),
        ("6. 执行爬虫", "点击执行按钮开始数据采集"),
        ("7. 查看结果", "在右侧文件列表中下载导出文件")
    ]
    
    for step, description in steps:
        print(f"\n{step}")
        print(f"   {description}")
    
    print(f"\n💡 提示:")
    print(f"   • 韭研数据需要手动登录")
    print(f"   • 开盘啦和龙虎榜数据可自动获取")
    print(f"   • 支持批量获取多种数据类型")
    print(f"   • 所有数据自动处理并导出为Excel格式")

if __name__ == "__main__":
    success = demo_platform()
    
    if success:
        show_quick_start()
    else:
        print("\n❌ 演示过程中出现错误，请检查环境配置")
        sys.exit(1)
