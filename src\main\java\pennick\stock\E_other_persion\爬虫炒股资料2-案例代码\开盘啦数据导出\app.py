from flask import Flask, render_template, request, Response, stream_with_context
import pandas as pd
from datetime import datetime
import os
from utils import download_stock_data

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/download', methods=['POST'])
def download():
    data = request.get_json()
    start_date = data['start_date']
    end_date = data['end_date']

    # 获取交易日期列表
    trading_days = pd.bdate_range(start=start_date, end=end_date)
    
    def generate():
        for date in trading_days:
            date_str = date.strftime('%Y-%m-%d')
            
            # 创建日期文件夹
            folder_path = os.path.join('downloads', date_str)
            os.makedirs(folder_path, exist_ok=True)
            
            yield f'处理日期: {date_str}\n'
            
            # 下载各类数据
            file_types = ['曾跌停', '跌停', '竞价', '炸板', '涨停', '自然涨停']
            for file_type in file_types:
                try:
                    download_stock_data(date_str, file_type, folder_path)
                    yield f'已下载 {date_str} {file_type}\n'
                except Exception as e:
                    yield f'下载 {date_str} {file_type} 失败: {str(e)}\n'
            
            yield f'完成日期: {date_str}\n'

    return Response(stream_with_context(generate()), mimetype='text/plain')

if __name__ == '__main__':
    os.makedirs('downloads', exist_ok=True)
    app.run(debug=True, port=5026) 