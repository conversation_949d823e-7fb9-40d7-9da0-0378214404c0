import pywencai
import pandas as pd
import pandas_market_calendars as mcal

# 设置显示选项以显示所有列
pd.set_option('display.max_columns', None)

# 获取中国A股市场日历
cal = mcal.get_calendar('SSE')

# 获取2024-10-31及之后的交易日
start_date = '2025-03-13'
end_date = '2025-03-14'  # 这里设置结束日期
schedule = cal.schedule(start_date=start_date, end_date=end_date)
dates = schedule.index.strftime('%Y-%m-%d').tolist()
print(f"查询到 {len(dates)} 个符合条件的日期。")

for i, date in enumerate(dates):
    print(f"\n正在处理日期: {date}")
    # 获取下一个交易日的日期
    if i < len(dates) - 1:
        next_trading_date = dates[i + 1]
    else:
        print("没有下一个交易日了，跳过。")
        continue
    # 构建查询语句
    query_str = f"{date}，全A，{next_trading_date}竞价涨幅，{next_trading_date}竞价价格"
    print(f"构建的查询语句: {query_str}")
    print("正在请求接口...")
    res = pywencai.get(query=query_str, sort_key='', sort_order='asc', loop=True)

    # 如果 res 是字典，将其转换为 DataFrame
    if isinstance(res, dict):
        res = pd.DataFrame(res)

    if res is None or res.empty:
        file_name = f"{date}空.xlsx"
        print(f"接口未返回数据，将保存为空文件: {file_name}")
        # 若 res 为 None，创建空的 DataFrame
        if res is None:
            res = pd.DataFrame()
    else:
        file_name = f"{date}.xlsx"
        print(f"接口返回了 {len(res)} 条数据，将保存到文件: {file_name}")

    # 将数据保存到 Excel 文件
    print(f"正在将数据保存到 {file_name}...")
    res.to_excel(file_name, index=False)
    print(f"数据已成功保存到 {file_name}。")