<!DOCTYPE html>
<html>
<head>
    <title>开盘啦涨停、跌停、炸板 - 历史数据下载器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <meta charset="UTF-8">
</head>
<body>
    <div class="container">
        <h2>开盘啦涨停、跌停、炸板 - 历史数据下载器</h2>
        <div class="form-group">
            <label for="start_date">需要获取数据的开始日期:</label>
            <input type="date" id="start_date" name="start_date" value="" onchange="saveDates()">
        </div>
        <div class="form-group">
            <label for="end_date">需要获取数据的结束日期:</label>
            <input type="date" id="end_date" name="end_date" value="" onchange="saveDates()">
        </div>
        <button onclick="downloadData()">批量下载历史数据</button>
        <div id="status" class="status"></div>
    </div>

    <script>
        function saveDates() {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            localStorage.setItem('startDate', startDate);
            localStorage.setItem('endDate', endDate);
        }
        
        // 设置默认日期为当前日期
        const today = new Date().toISOString().split('T')[0];
        
        // 从本地存储获取之前的日期，如果没有则使用当前日期
        const savedStartDate = localStorage.getItem('startDate') || today;
        const savedEndDate = localStorage.getItem('endDate') || today;
        
        document.getElementById('start_date').value = savedStartDate;
        document.getElementById('end_date').value = savedEndDate;
        
        async function downloadData() {
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            
            // 保存到本地存储
            localStorage.setItem('startDate', startDate);
            localStorage.setItem('endDate', endDate);
            
            const statusDiv = document.getElementById('status');

            if (!startDate || !endDate) {
                alert('请选择开始日期和结束日期');
                return;
            }

            statusDiv.innerHTML = '开始下载数据...';

            try {
                const response = await fetch('/download', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        start_date: startDate,
                        end_date: endDate
                    })
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const {value, done} = await reader.read();
                    if (done) break;
                    
                    const text = decoder.decode(value);
                    statusDiv.innerHTML += '<br>' + text;
                }

                statusDiv.innerHTML += '<br>下载完成！';
            } catch (error) {
                statusDiv.innerHTML += '<br>下载出错: ' + error;
            }
        }
    </script>
</body>
</html> 