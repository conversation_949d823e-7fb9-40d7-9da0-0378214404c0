from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import time
import atexit
import psutil
import os
import signal
import glob
import re

def kill_chrome_processes():
    """结束所有Chrome相关进程"""
    try:
        print("正在清理Chrome进程...")
        for proc in psutil.process_iter(['pid', 'name']):
            # 查找Chrome相关进程
            if any(chrome_process in proc.info['name'].lower() 
                  for chrome_process in ['chrome', 'chromedriver']):
                try:
                    print(f"正在结束进程: {proc.info['name']} (PID: {proc.info['pid']})")
                    os.kill(proc.info['pid'], signal.SIGTERM)
                except Exception as e:
                    print(f"结束进程失败: {str(e)}")
                    continue
        print("Chrome进程清理完成")
    except Exception as e:
        print(f"清理Chrome进程时出错: {str(e)}")

def wait_and_find_element(driver, by, value, timeout=10):
    """等待并查找元素"""
    try:
        element = driver.find_element(by, value)
        print(f"成功找到元素: {by}={value}")
        return element
    except Exception as e:
        print(f"未找到元素: {by}={value}, 错误信息: {str(e)}")
        return None

def scroll_to_bottom(driver, step=400, delay=0.01):
    """模拟渐进式滚动到页面底部"""
    print("开始模拟渐进式滚动到页面底部...")
    
    # 获取初始滚动高度
    total_height = driver.execute_script("return document.body.scrollHeight")
    current_position = 0
    
    while current_position < total_height:
        # 计算下一个滚动位置
        current_position += step
        
        # 滚动到新位置
        driver.execute_script(f"window.scrollTo(0, {current_position});")
        print(f"滚动到位置: {current_position}/{total_height}")
        
        # 等待内容加载
        time.sleep(delay)
        
        # 重新获取页面高度（可能因为图片加载而变化）
        new_height = driver.execute_script("return document.body.scrollHeight")
        if new_height > total_height:
            total_height = new_height
            print(f"页面高度更新为: {total_height}")
    
    # 最后确保滚动到底部
    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
    print("已滚动到页面底部")

def process_article(driver):
    """处理单篇文章的函数"""
    # 添加滚动到底部的逻辑
    scroll_to_bottom(driver)
    print("页面滚动完成，开始获取内容...")
    
    # 直接获取标题
    print("正在获取文章标题...")
    title = "未知标题"
    
    # 尝试多种方式获取标题
    try:
        title_element = driver.find_element(By.ID, "stockTitle")
        if title_element:
            title = title_element.text.strip()
            print(f"通过ID成功获取标题: {title}")
    except:
        try:
            title_element = driver.find_element(By.CLASS_NAME, "article-tittle")
            if title_element:
                title = title_element.text.strip()
                print(f"通过class成功获取标题: {title}")
        except:
            try:
                title = driver.execute_script("""
                    var titleElement = document.querySelector('.article-tittle');
                    return titleElement ? titleElement.textContent.trim() : '未知标题';
                """)
                if title != "未知标题":
                    print(f"通过JavaScript成功获取标题: {title}")
            except Exception as e:
                print(f"获取标题失败: {str(e)}")
    
    # 直接获取作者信息
    print("正在获取作者信息...")
    username = "未知作者"
    try:
        author_element = driver.find_element(By.CLASS_NAME, "article-author")
        if author_element:
            username = author_element.text.strip()
            print(f"成功获取作者: {username}")
    except Exception as e:
        print(f"获取作者信息失败: {str(e)}")
        
    # 等待并获取正文内容
    print("正在获取文章正文...")
    article_content = wait_and_find_element(driver, By.CLASS_NAME, "article-text")
    if article_content:
        # 获取HTML内容而不是纯文本
        content = article_content.get_attribute('innerHTML')
        print("成功获取正文内容")
    else:
        print("未找到正文内容")
        content = "<p>未找到内容</p>"
    
    # 获取发布时间
    print("正在获取发布时间...")
    publish_date = "未知日期"
    try:
        # 定位包含日期的span元素
        date_element = driver.find_element(By.XPATH, "//div[@class='article-data']//span[contains(text(), '-')]")
        if date_element:
            # 使用字符串处理提取日期部分
            date_text = date_element.text.strip()
            # 查找形如 YYYY-MM-DD 的日期格式
            date_match = re.search(r'\d{4}-\d{2}-\d{2}', date_text)
            if date_match:
                publish_date = date_match.group()
                print(f"成功获取发布日期: {publish_date}")
    except Exception as e:
        print(f"获取发布日期失败: {str(e)}")
    
    # 修改文件名生成逻辑，添加发布日期前缀
    filename = f"{publish_date}_{title.replace(' ', '_').replace('/', '_')}.html"
    
    with open(filename, "w", encoding="utf-8") as f:
        f.write("""<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>{title}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0 auto;
            max-width: 800px;
            padding: 20px;
        }}
        h1 {{
            text-align: center;
            color: #333;
        }}
        .article-meta {{
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }}
        .article-content {{
            margin-top: 20px;
        }}
        /* 保留原文格式 */
        .article-content p {{
            margin: 1em 0;
        }}
        .article-content br {{
            display: block;
        }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    <div class="article-meta">
        <p>作者: {username}</p>
        <p>发布日期: {publish_date}</p>
    </div>
    <div class="article-content">
        {content}
    </div>
</body>
</html>""".format(title=title, username=username, content=content, publish_date=publish_date))
        
    print(f"文章已保存为: {filename}")
    print(f"标题: {title}")
    print(f"作者: {username}")
    
    # 获取完成后等待2秒
    time.sleep(1)
    
    # 主动退出浏览器
    print("内容获取完成,正在关闭浏览器...")
    return title  # 返回标题用于记录

def get_article_links():
    """获取所有以article_links开头的txt文件中的链接"""
    links = []
    for file_path in glob.glob("article_links*.txt"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                links.extend([line.strip() for line in f if line.strip()])
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {str(e)}")
    return links

def open_page():
    driver = None
    try:
        # 配置Chrome选项
        print("正在配置Chrome选项...")
        chrome_options = Options()
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument('--disable-background-networking')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        # 添加启动时最大化窗口的选项
        chrome_options.add_argument('--start-maximized')
        
        # 添加User-Agent
        chrome_options.add_argument('user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')
        
        # 添加实验性选项
        chrome_options.add_experimental_option('excludeSwitches', ['enable-automation'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 创建Chrome驱动
        print("正在创建Chrome驱动...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # 确保窗口最大化
        driver.maximize_window()
        print("浏览器窗口已最大化")
        
        # 设置页面加载超时时间为2秒
        driver.set_page_load_timeout(3)
        
        # 获取所有要处理的链接
        article_links = get_article_links()
        if not article_links:
            print("未找到任何文章链接")
            return
        
        print(f"共找到 {len(article_links)} 个链接待处理")
        
        # 首先打开第一个链接并登录
        first_url = article_links[0]
        print(f"正在打开第一个页面进行登录: {first_url}")
        try:
            driver.get(first_url)
        except TimeoutException:
            print("页面加载超时，继续执行...")
        
        # 等待页面基本加载
        print("等待页面基本加载 2 秒...")
        time.sleep(2)
        
        # 查找并点击登录/注册按钮
        print("正在查找登录/注册按钮...")
        # 尝试多种定位方式
        login_btn = wait_and_find_element(driver, By.CSS_SELECTOR, "a.header-a[onclick='loginPanel()']")
        if not login_btn:
            login_btn = wait_and_find_element(driver, By.XPATH, "//a[@class='header-a' and @onclick='loginPanel()']")
        
        if login_btn:
            print("找到登录/注册按钮,准备点击...")
            # 使用JavaScript点击以避免可能的遮挡问题
            driver.execute_script("arguments[0].click();", login_btn)
            print("已点击登录/注册按钮")
            
            # 等待账号登录按钮出现
            print("正在查找账号登录按钮...")
            account_login_btn = wait_and_find_element(driver, By.ID, "userLoginBtn")
            if account_login_btn:
                print("找到账号登录按钮,准备点击...")
                account_login_btn.click()
                print("已点击账号登录按钮")
                
                # 输入用户名
                print("正在查找用户名输入框...")
                username_input = wait_and_find_element(driver, By.ID, "userPanelName")
                if username_input:
                    print("找到用户名输入框,准备输入...")
                    username_input.clear()
                    username_input.send_keys("spenser")
                    print("已输入用户名")
                    time.sleep(1)
                else:
                    print("未找到用户名输入框")
                
                # 输入密码
                print("正在查找密码输入框...")
                password_input = wait_and_find_element(driver, By.ID, "userPanelPwd") 
                if password_input:
                    print("找到密码输入框,准备输入...")
                    password_input.clear()
                    password_input.send_keys("eight986")
                    print("已输入密码")
                    time.sleep(1)
                else:
                    print("未找到密码输入框")
                
                # 点击登录按钮
                print("正在查找登录提交按钮...")
                login_button = wait_and_find_element(driver, By.ID, "loginBtn")
                if login_button:
                    print("找到登录提交按钮,准备点击...")
                    login_button.click()
                    print("已点击登录提交按钮")
                    time.sleep(2)
                    
                    # 登录成功后，首先处理当前页面（第一个链接）
                    print("\n正在处理第 1 个链接（当前页面）...")
                    title = process_article(driver)
                    processed_articles = [f"{title} - {first_url}"]
                    
                    # 然后处理剩余的链接
                    for i, url in enumerate(article_links[1:], 2):  # 从第2个开始
                        try:
                            print(f"\n正在处理第 {i}/{len(article_links)} 个链接: {url}")
                            driver.get(url)
                            time.sleep(2)  # 等待页面加载
                            
                            title = process_article(driver)
                            processed_articles.append(f"{title} - {url}")
                            
                            time.sleep(1)  # 处理完一篇文章后稍作等待
                            
                        except Exception as e:
                            print(f"处理链接 {url} 时出错: {str(e)}")
                            continue
                    
                    # 保存处理记录
                    try:
                        with open("processed_articles.txt", "w", encoding="utf-8") as f:
                            f.write("\n".join(processed_articles))
                        print("\n处理完成的文章列表已保存到 processed_articles.txt")
                    except Exception as e:
                        print(f"保存处理记录时出错: {str(e)}")
                    
                    return
                else:
                    print("未找到登录提交按钮")
            else:
                print("未找到账号登录按钮")
        else:
            print("未找到登录/注册按钮")
            
        # 如果登录失败,等待10秒后退出
        time.sleep(10)
        
        # 保持窗口打开
        time.sleep(1)
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
    
    finally:
        try:
            if driver:
                print("正在关闭浏览器...")
                # 关闭所有窗口
                driver.quit()
                print("浏览器已关闭")
                # 确保进程被终止
                kill_chrome_processes()
        except Exception as e:
            print(f"关闭浏览器时出错: {str(e)}")
            # 如果正常关闭失败，强制结束进程
            kill_chrome_processes()

if __name__ == '__main__':
    # 注册程序退出时的清理函数
    atexit.register(kill_chrome_processes)
    
    try:
        open_page()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    finally:
        # 确保在程序结束时清理所有Chrome进程
        kill_chrome_processes()
