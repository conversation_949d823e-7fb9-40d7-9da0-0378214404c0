"""
开盘啦数据爬虫 - 实际功能版本
"""
import requests
from typing import List, Dict, Any
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.base_crawler import BaseCrawler

class KaipanlaCrawler(BaseCrawler):
    """开盘啦数据爬虫"""
    
    def __init__(self):
        config = {
            "name": "开盘啦数据",
            "base_url": "https://apphis.longhuvip.com",
            "rate_limit": 0.5,
            "description": "涨停、跌停、竞价等数据",
            "data_types": ["曾跌停", "跌停", "竞价", "炸板", "涨停", "自然涨停"]
        }
        super().__init__("kaipanla", config)
        
        # URL模板映射
        self.url_templates = {
            '曾跌停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=5&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
            '跌停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=3&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=4&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
            '竞价': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=1&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=1&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
            '炸板': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=2&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=3&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
            '涨停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=1&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=2&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
            '自然涨停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=4&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&'
        }
        
    def fetch_data(self, date: str = None, data_type: str = "涨停", **kwargs) -> List[Dict[str, Any]]:
        """获取开盘啦数据"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
            
        if data_type not in self.url_templates:
            self.logger.error(f"不支持的数据类型: {data_type}")
            return []
            
        try:
            self.logger.info(f"开始获取开盘啦数据: {date} - {data_type}")
            
            # 获取对应的URL模板
            url_base = self.url_templates[data_type]
            url = url_base.format(date)
            
            # 初始化数据列表
            all_data = []
            index = 0
            
            # 分页获取数据
            while True:
                current_url = url + f'&Index={index}'
                
                response = self.make_request(current_url)
                if not response:
                    break
                    
                try:
                    json_data = response.json()
                except Exception as e:
                    self.logger.error(f"解析JSON失败: {e}")
                    break
                
                if 'list' in json_data:
                    data = json_data['list']
                    if not data:
                        break
                    all_data.extend(data)
                    index += 60
                    self.logger.info(f"已获取 {len(all_data)} 条数据...")
                else:
                    self.logger.warning("响应中没有'list'字段")
                    break
                    
            self.logger.info(f"成功获取开盘啦数据: {len(all_data)}条 - {data_type}")
            
            # 转换数据格式
            formatted_data = self._format_data(all_data, data_type)
            return formatted_data
            
        except Exception as e:
            self.logger.error(f"获取开盘啦数据失败: {e}")
            return []
            
    def fetch_all_types(self, date: str = None) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有类型的数据"""
        if not date:
            date = datetime.now().strftime('%Y-%m-%d')
            
        results = {}
        
        for data_type in self.config["data_types"]:
            self.logger.info(f"正在获取 {data_type} 数据...")
            data = self.fetch_data(date=date, data_type=data_type)
            results[data_type] = data
            
        return results
        
    def _format_data(self, raw_data: List, data_type: str) -> List[Dict[str, Any]]:
        """格式化数据"""
        if not raw_data:
            return []
            
        formatted_data = []
        
        # 根据数据类型定义列名
        column_mappings = {
            "涨停": ["股票代码", "股票名称", "最新价", "涨跌幅", "首次涨停时间", "最后涨停时间", "涨停强度", "流通市值"],
            "跌停": ["股票代码", "股票名称", "最新价", "涨跌幅", "首次跌停时间", "最后跌停时间", "跌停强度", "流通市值"],
            "竞价": ["股票代码", "股票名称", "最新价", "竞价时间", "竞价金额", "竞价量"],
            "炸板": ["股票代码", "股票名称", "最新价", "涨跌幅", "炸板时间", "炸板次数", "涨停强度", "流通市值"],
            "曾跌停": ["股票代码", "股票名称", "最新价", "涨跌幅", "首次跌停时间", "最后跌停时间", "跌停强度", "流通市值"],
            "自然涨停": ["股票代码", "股票名称", "最新价", "涨跌幅", "首次涨停时间", "最后涨停时间", "涨停强度", "流通市值"]
        }
        
        columns = column_mappings.get(data_type, [f"列{i+1}" for i in range(len(raw_data[0]) if raw_data else 0)])
        
        for item in raw_data:
            if isinstance(item, list):
                # 确保数据长度与列名匹配
                while len(item) < len(columns):
                    item.append("")
                    
                formatted_item = {}
                for i, column in enumerate(columns):
                    if i < len(item):
                        formatted_item[column] = item[i]
                    else:
                        formatted_item[column] = ""
                        
                formatted_data.append(formatted_item)
            else:
                formatted_data.append(item)
                
        return formatted_data

def test_kaipanla_crawler():
    """测试开盘啦爬虫"""
    crawler = KaipanlaCrawler()
    try:
        # 测试单个数据类型
        data = crawler.fetch_data(data_type="涨停")
        print(f"获取到涨停数据 {len(data)} 条")
        if data:
            print("前3条数据:")
            for i, item in enumerate(data[:3]):
                print(f"{i+1}. {item}")
                
    finally:
        crawler.cleanup()

if __name__ == "__main__":
    test_kaipanla_crawler()
