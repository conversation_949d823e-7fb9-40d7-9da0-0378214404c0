<!DOCTYPE html>
<html>

<head>
    <title>股票分笔数据图表</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 主容器：使用flex布局，设置内边距和元素间距 */
        .container {
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        /* 表单容器：固定宽度300px */
        .form-container {
            flex: 0 0 300px;
        }

        /* 图表容器：使用flex布局，占据剩余空间 */
        .chart-container {
            flex: 1;
            display: flex;
        }

        /* 图表基础样式：最小高度和边框设置 */
        .chart {
            min-height: 600px;
            border: 1px solid #ddd;
            border-right: none;
        }

        /* 竞价图表：固定宽度200px */
        #bidChart {
            flex: 0 0 200px;
        }

        /* 主图表：自适应占据剩余空间 */
        #mainChart {
            flex: 1;
        }


        /* 表单组样式：设置底部间距和相对定位 */
        .form-group {
            margin-bottom: 10px;
            position: relative;
        }

        /* 表单标签：固定宽度和内联块级显示 */
        .form-group label {
            display: inline-block;
            width: 80px;
        }

        /* 输入框样式：内边距和宽度设置 */
        .form-group input {
            padding: 5px;
            width: 150px;
            padding-right: 25px;
        }

        /* 隐藏IE浏览器的清除按钮 */
        .form-group input::-ms-clear {
            display: none;
        }

        /* 自定义清除按钮样式 */
        .form-group .clear-icon {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            cursor: pointer;
            display: none;
        }

        /* 清除按钮的交叉线样式 */
        .form-group .clear-icon::before,
        .form-group .clear-icon::after {
            content: '';
            position: absolute;
            width: 2px;
            height: 12px;
            background-color: #999;
            left: 50%;
            top: 50%;
        }

        /* 清除按钮的第一条交叉线 */
        .form-group .clear-icon::before {
            transform: translate(-50%, -50%) rotate(45deg);
        }

        /* 清除按钮的第二条交叉线 */
        .form-group .clear-icon::after {
            transform: translate(-50%, -50%) rotate(-45deg);
        }

        /* 输入框非空时显示清除按钮 */
        .form-group input:not(:placeholder-shown)+.clear-icon {
            display: block;
        }

        /* 按钮基础样式 */
        button {
            padding: 8px 15px;
            background-color: #414141;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* 按钮悬停效果 */
        button:hover {
            background-color: #242424;
        }
    </style>
</head>

<body>
    
    <div class="container">
        
        <div class="form-container">
            <h3 style="text-align: left; margin: 5px 0;">历史竞价图与超盘图 查询工具</h3>
            <div class="form-group">
                <label for="date">日期：</label>
                <input type="text" id="date" placeholder="格式：YYYY-MM-DD" onfocus="checkClipboardForDate()">
                <span class="clear-icon" onclick="clearInput('date')"></span>
            </div>
            <div class="form-group">
                <label for="symbol">股票代码：</label>
                <input type="text" id="symbol" placeholder="例如：603106" onfocus="checkClipboardForSymbol()">
                <span class="clear-icon" onclick="clearInput('symbol')"></span>
            </div>
            <button onclick="fetchData()">绘制图表</button>
        </div>
        <div class="chart-container">
            <div id="bidChart" class="chart"></div>
            <div id="mainChart" class="chart"></div>
        </div>
    </div>

    <script>
        var mainChart = null;
        var bidChart = null;

        function initCharts() {
            if (!mainChart) {
                mainChart = echarts.init(document.getElementById('mainChart'));
            }
            if (!bidChart) {
                bidChart = echarts.init(document.getElementById('bidChart'));
            }
        }

        async function checkClipboardForSymbol() {
            try {
                const text = await navigator.clipboard.readText();
                const symbolRegex = /^\d{6}$/;
                if (symbolRegex.test(text)) {
                    document.getElementById('symbol').value = text;
                }
            } catch (err) {
                console.log('无法读取剪贴板内容');
            }
        }

        function clearInput(inputId) {
            document.getElementById(inputId).value = '';
        }

        async function checkClipboardForDate() {
            try {
                const text = await navigator.clipboard.readText();
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (dateRegex.test(text)) {
                    document.getElementById('date').value = text;
                }
            } catch (err) {
                console.log('无法读取剪贴板内容');
            }
        }

        async function fetchData() {
            var dateInput = document.getElementById('date').value;
            var symbol = document.getElementById('symbol').value;

            if (!dateInput || !symbol) {
                alert('请输入日期和股票代码');
                return;
            }

            // 验证日期格式
            var dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(dateInput)) {
                alert('请输入正确的日期格式：YYYY-MM-DD');
                return;
            }

            // 转换日期格式：从YYYY-MM-DD到YYYYMMDD
            var date = dateInput.replace(/-/g, '');

            try {
                const [stockResponse, bidResponse] = await Promise.all([
                    fetch(`/api/stock_data?date=${date}&symbol=${symbol}`),
                    fetch(`/api/bid_data?date=${date}&symbol=${symbol}`)
                ]);

                if (!stockResponse.ok || !bidResponse.ok) {
                    throw new Error('网络响应失败');
                }

                const stockData = await stockResponse.json();
                const bidData = await bidResponse.json();

                updateCharts(stockData, bidData);
            } catch (error) {
                alert('获取数据失败：' + error);
            }
        }

        function updateCharts(stockData, bidData) {
            initCharts();

            // 计算价格范围
            const symbol = document.getElementById('symbol').value;
            let priceRange = {
                max: bidData.preclose_px * 1.1,
                min: bidData.preclose_px * 0.9
            };
            
            if (symbol.startsWith('68') || symbol.startsWith('30')) {
                priceRange.max = Number((bidData.preclose_px * 1.2).toFixed(2));
                priceRange.min = Number((bidData.preclose_px * 0.8).toFixed(2));
            } else if (symbol.startsWith('6') || symbol.startsWith('0')) {
                priceRange.max = Number((bidData.preclose_px * 1.1).toFixed(2));
                priceRange.min = Number((bidData.preclose_px * 0.9).toFixed(2));
            }

            // 竞价数据图表配置
            const bidOption = {
                title: {
                    text: '竞价数据图',
                    top: 0
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function (params) {
                        if (params[0].seriesName === '成交量') {
                            return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}`;
                        }
                        const price = params[0].data;
                        const priceChange = ((price - bidData.preclose_px) / bidData.preclose_px * 100).toFixed(2);
                        const sign = priceChange >= 0 ? '+' : '';
                        return `${params[0].name}<br/>${params[0].seriesName}: ${price}<br/>涨跌幅: ${sign}${priceChange}%`;
                    }
                },
                grid: [{
                    left: '10%',
                    right: '0%',
                    height: '50%'
                }, {
                    left: '10%',
                    right: '0%',
                    top: '63%',
                    height: '25%'
                }],
                xAxis: [{
                    type: 'category',
                    data: bidData.bid.map(item => item[0]),
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    axisLabel: {
                        rotate: 45
                    },
                    splitLine: { show: false }
                }, {
                    type: 'category',
                    gridIndex: 1,
                    data: bidData.bid.map(item => item[0]),
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    axisLabel: { show: false },
                    splitLine: { show: false }
                }],
                yAxis: [{
                    type: 'value',
                    scale: false,
                    splitArea: {
                        show: true
                    },
                    min: priceRange.min,
                    max: priceRange.max,
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: { show: false },
                    splitLine: { show: false }
                }, {
                    name: '成交量',
                    type: 'value',
                    gridIndex: 1,
                    splitNumber: 2,
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { show: false },
                    axisLabel: { show: false }
                }],
                dataZoom: [{
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: 0,
                    end: 100
                }, {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    bottom: '0%',
                    start: 0,
                    end: 100
                }],
                series: [{
                    name: '价格',
                    type: 'line',
                    data: bidData.bid.map(item => item[1]),
                    showSymbol: false,
                    markPoint: {
                        data: [{
                            coord: [bidData.bid[bidData.bid.length - 1][0], bidData.bid[bidData.bid.length - 1][1]],
                            symbol: 'circle',
                            symbolSize: 3,
                            itemStyle: {
                                color: '#000'
                            },
                            label: {
                                show: true,
                                formatter: function(params) {
                                    const priceChange = ((params.data.coord[1] - bidData.preclose_px) / bidData.preclose_px * 100).toFixed(2);
                                    const sign = priceChange >= 0 ? '+' : '';
                                    return sign + priceChange + '%';
                                },
                                position: 'left',
                                distance: 5,
                                fontSize: 12
                            }
                        }]
                    },
                    markLine: {
                        symbol: ['none', 'none'],
                        label: { show: false },
                        data: [{
                            yAxis: bidData.preclose_px,
                            lineStyle: {
                                color: '#999',
                                type: 'dashed'
                            }
                        }]
                    },
                    lineStyle: {
                        width: 1
                    }
                }, {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: bidData.bid.map(item => ({
                        value: item[3],
                        itemStyle: {
                            color: item[2] === 0 ? '#00da3c' : '#ec0000'
                        }
                    }))
                }]
            };

            // 分笔数据图表配置
            const mainOption = {
                title: {
                    text: '分笔数据（超盘图）',
                    top: 0
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function (params) {
                        if (params[0].seriesName === '成交量') {
                            return `${params[0].name}<br/>${params[0].seriesName}: ${params[0].value}`;
                        }
                        const price = params[0].data;
                        const priceChange = ((price - bidData.preclose_px) / bidData.preclose_px * 100).toFixed(2);
                        const sign = priceChange >= 0 ? '+' : '';
                        return `${params[0].name}<br/>${params[0].seriesName}: ${price}<br/>涨跌幅: ${sign}${priceChange}%`;
                    }
                },
                grid: [{
                    left: '0%',
                    right: '0%',
                    height: '50%'
                }, {
                    left: '0%',
                    right: '0%',
                    top: '63%',
                    height: '25%'
                }],
                xAxis: [{
                    type: 'category',
                    data: stockData.time,
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    axisLabel: {
                        rotate: 45
                    },
                    splitLine: { show: false }
                }, {
                    type: 'category',
                    gridIndex: 1,
                    data: stockData.time,
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    axisLabel: { show: false },
                    splitLine: { show: false }
                }],
                yAxis: [{
                    type: 'value',
                    scale: false,
                    splitArea: {
                        show: true
                    },
                    min: priceRange.min,
                    max: priceRange.max,
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: { show: false },
                    splitLine: { show: false }
                }, {
                    name: '成交量',
                    type: 'value',
                    gridIndex: 1,
                    splitNumber: 2,
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { show: false },
                    axisLabel: { show: false }
                }],
                dataZoom: [{
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: 0,
                    end: 100
                }, {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    bottom: '0%',
                    start: 0,
                    end: 100
                }],
                series: [{
                    name: '价格',
                    type: 'line',
                    data: stockData.price,
                    showSymbol: false,
                    markLine: {
                        symbol: ['none', 'none'],
                        label: { show: false },
                        data: [
                            {
                                yAxis: bidData.preclose_px,
                                lineStyle: {
                                    color: '#999',
                                    type: 'dashed'
                                }
                            },
                            {
                                xAxis: stockData.time.findIndex(time => time.startsWith('13:00')),
                                lineStyle: {
                                    color: '#E0E0E0',
                                    width: 1,
                                    type: 'solid'
                                }
                            }
                        ]
                    },
                    lineStyle: {
                        width: 1
                    }
                }, {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: stockData.vol.map((value, index) => ({
                        value: value,
                        itemStyle: {
                            color: stockData.buyorsell[index] === 1 ? '#00da3c' : '#ec0000'
                        }
                    }))
                }]
            };

            bidChart.setOption(bidOption);
            mainChart.setOption(mainOption);
        }

        // 窗口大小改变时，调整图表大小
        window.addEventListener('resize', function() {
            if (mainChart) {
                mainChart.resize();
            }
            if (bidChart) {
                bidChart.resize();
            }
        });
    </script>
</body>

</html>