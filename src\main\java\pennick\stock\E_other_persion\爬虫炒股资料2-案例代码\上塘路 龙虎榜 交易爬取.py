import requests
from openpyxl import Workbook


# 假设函数用于从API获取数据
def fetch_data(page):
    url = 'https://applhb.longhuvip.com/w1/api/index.php?apiv=w36&PhoneOSNew=1&VerSion=********'
    post_data = {
        'c': 'BusinessGroup',
        'a': 'GroupLog',
        'GID': '22', #养家20,64上塘路
        'st': '200',
        'Index': page * 120,
        'Money': '5000000',
        'Order': '2',
        'SDay': '0',
        'Day': '24',
    }
    response = requests.post(url, data=post_data)
    if response.status_code == 200:
        return response.json()
    else:
        return None

    # 初始化工作簿和工作表


wb = Workbook()
ws = wb.active
ws.title = "StockData"

# 写入标题行
header = ['D3', 'Time', 'StockID', 'Name', 'IncreaseAmount', 'Buy', 'Sell']
ws.append(header)

# 遍历页面并获取数据
data_list = []  # 创建一个列表来存储所有数据项
for page in range(10):
    data = fetch_data(page)
    if data and 'list' in data:
        data_list.extend(data['list'])  # 将每页的数据添加到列表中

# 反转数据列表以实现倒序
data_list.reverse()

# 写入标题行
header = ['D3', 'Time', 'StockID', 'Name', 'IncreaseAmount', 'Buy', 'Sell']
ws.append(header)

# 遍历反转后的数据列表并写入Excel
for item in data_list:
    # 转换为适合写入Excel的格式（如果Buy和Sell是字符串，转换为浮点数后除以10000，并保留两位小数）
    for field in ['Buy', 'Sell']:
        if isinstance(item[field], str) and item[field].replace('.', '', 1).isdigit():
            item[field] = round(float(item[field]) / 10000, 2)
        elif isinstance(item[field], (int, float)):
            item[field] = round(item[field] / 10000, 2)

            # 根据'D3'的值修改写入的内容
    d3_value = item['D3']  # 假设这里d3_value是一个字符串
    if d3_value == "1":
        d3_display = "3日"
    elif d3_value == "0":
        d3_display = ""
    else:
        d3_display = d3_value

        # 写入数据行
    ws.append([d3_display, item['Time'], item['StockID'], item['Name'], item['IncreaseAmount'], item['Buy'],
               item['Sell']])

# 保存工作簿（现在是在所有数据都已写入后）
excel_filename = 'output.xlsx'
wb.save(excel_filename)

print(f"ok")