"""
数据处理模块
"""
import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime
import json

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("data_processor")
        
    def clean_stock_code(self, code: str) -> str:
        """清理股票代码"""
        if isinstance(code, str):
            return code.replace("sh", "").replace("sz", "").replace("bj", "")
        return str(code) if code else ""
        
    def process_timestamp(self, timestamp: int) -> str:
        """处理时间戳"""
        try:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%H:%M')
        except (ValueError, TypeError):
            return str(timestamp)
            
    def divide_by_factor(self, value: Any, factor: float) -> float:
        """按因子除法"""
        try:
            return round(float(value) / factor, 2)
        except (ValueError, TypeError):
            return value
            
    def process_jiuyan_data(self, data: List[Dict]) -> List[Dict]:
        """处理韭研数据"""
        processed_data = []
        
        for item in data:
            processed_item = {
                "股票代码": self.clean_stock_code(item.get("股票代码", "")),
                "股票名称": item.get("股票名称", ""),
                "板块": item.get("板块", ""),
                "原因详解": item.get("原因详解", "")
            }
            
            # 处理板块信息中的"1、"分割
            reason = processed_item["原因详解"]
            if reason and "1、" in reason:
                parts = reason.split("1、", 1)
                if len(parts) == 2:
                    processed_item["板块"] = parts[0].strip()
                    processed_item["原因详解"] = "1、" + parts[1].strip()
                    
            processed_data.append(processed_item)
            
        return processed_data
        
    def process_kaipanla_data(self, data: List[Dict], data_type: str) -> List[Dict]:
        """处理开盘啦数据"""
        processed_data = []
        
        # 数据处理配置
        config = {
            "曾跌停": {
                "timestamp_columns": [4, 5],  # 时间戳列索引
                "divide_columns": {6: 10000, 7: 10000}  # 需要除法的列
            },
            "跌停": {
                "timestamp_columns": [4, 5],
                "divide_columns": {6: 10000, 7: 10000}
            },
            "竞价": {
                "timestamp_columns": [3],
                "divide_columns": {4: 10000, 5: 10000}
            },
            "炸板": {
                "timestamp_columns": [4, 5],
                "divide_columns": {6: 10000, 7: 10000}
            },
            "涨停": {
                "timestamp_columns": [4, 5], 
                "divide_columns": {6: 10000, 7: 10000}
            },
            "自然涨停": {
                "timestamp_columns": [4, 5],
                "divide_columns": {6: 10000, 7: 10000}
            }
        }
        
        type_config = config.get(data_type, {})
        
        for item in data:
            if isinstance(item, list):
                processed_item = item.copy()
                
                # 处理时间戳
                for col_idx in type_config.get("timestamp_columns", []):
                    if col_idx < len(processed_item):
                        processed_item[col_idx] = self.process_timestamp(processed_item[col_idx])
                        
                # 处理除法
                for col_idx, divisor in type_config.get("divide_columns", {}).items():
                    if col_idx < len(processed_item):
                        processed_item[col_idx] = self.divide_by_factor(processed_item[col_idx], divisor)
                        
                processed_data.append(processed_item)
            else:
                processed_data.append(item)
                
        return processed_data
        
    def to_excel(self, data: List[Dict], filename: str, 
                 data_type: str = None) -> str:
        """导出到Excel"""
        try:
            from config.settings import EXPORT_DIR
            
            if not data:
                self.logger.warning("没有数据可导出")
                return None
                
            # 根据数据类型处理数据
            if data_type == "jiuyan":
                processed_data = self.process_jiuyan_data(data)
                df = pd.DataFrame(processed_data)
            elif data_type in ["曾跌停", "跌停", "竞价", "炸板", "涨停", "自然涨停"]:
                processed_data = self.process_kaipanla_data(data, data_type)
                df = pd.DataFrame(processed_data)
            else:
                df = pd.DataFrame(data)
            
            # 确保文件名有.xlsx扩展名
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
                
            filepath = EXPORT_DIR / filename
            
            # 使用pandas导出Excel
            df.to_excel(filepath, index=False, sheet_name='数据')
            
            self.logger.info(f"数据已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            return None
            
    def to_json(self, data: List[Dict], filename: str) -> str:
        """导出到JSON"""
        try:
            from config.settings import EXPORT_DIR
            
            if not filename.endswith('.json'):
                filename += '.json'
                
            filepath = EXPORT_DIR / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"数据已导出到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"导出JSON失败: {e}")
            return None
            
    def get_export_files(self) -> List[Dict[str, Any]]:
        """获取导出文件列表"""
        try:
            from config.settings import EXPORT_DIR
            
            files = []
            for file_path in EXPORT_DIR.glob("*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    files.append({
                        "name": file_path.name,
                        "path": str(file_path),
                        "size": stat.st_size,
                        "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
                    
            # 按修改时间倒序排列
            files.sort(key=lambda x: x["modified_time"], reverse=True)
            return files
            
        except Exception as e:
            self.logger.error(f"获取导出文件列表失败: {e}")
            return []

# 全局数据处理器实例
data_processor = DataProcessor()
