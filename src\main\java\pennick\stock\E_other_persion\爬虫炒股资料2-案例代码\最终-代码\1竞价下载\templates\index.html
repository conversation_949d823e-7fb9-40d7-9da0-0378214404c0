<!DOCTYPE html>
<html>
<head>
    <title>数据下载工具</title>
    <meta charset="UTF-8">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            background-color: #f5f5f5;
        }
        .main-title {
            color: rgb(245, 29, 29);
            text-align: center;
            font-size: 34px;
            margin-bottom: 30px;
            font-weight: bold;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #000;
            font-size: 18px;
        }
        .input-group {
            margin-bottom: 10px;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            color: #000;
        }
        input[type="text"],
        input[type="number"],
        input[type="date"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 150px;
            font-size: 14px;
        }
        input[type="radio"] {
            margin-right: 5px;
        }
        .radio-group {
            margin-bottom: 10px;
        }
        .radio-group label {
            margin-right: 20px;
            color: #000;
        }
        button {
            background-color: #000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #333;
        }
        #downloadStatus {
            color: #000;
            font-size: 14px;
            margin-left: 10px;
        }
        .hidden {
            display: none;
        }
        .error {
            color: red;
            margin-top: 10px;
            font-size: 14px;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1 class="main-title">实时龙虎榜 历史数据下载器</h1>
    <div class="section">
        <h3>日期设置</h3>
        <div class="radio-group">
            <label>
                <input type="radio" name="dateType" value="realtime"> 实时
            </label>
            <label>
                <input type="radio" name="dateType" value="history" checked> 指定历史日期
            </label>
        </div>
        <div id="dateRangeControl">
            <div class="input-group">
                <label>开始日期：</label>
                <input type="date" id="startDate">
            </div>
            <div class="input-group">
                <label>结束日期：</label>
                <input type="date" id="endDate">
            </div>
        </div>
    </div>

    <div class="section">
        <h3>时间区间</h3>
        <p class="description">时间最早0925，最晚1500，写四位数字即可</p>
        <div class="input-group">
            <label>开始时间：</label>
            <input type="text" id="rStart" value="0925" maxlength="4" pattern="\d{4}">
        </div>
        <div class="input-group">
            <label>结束时间：</label>
            <input type="text" id="rEnd" value="0930" maxlength="4" pattern="\d{4}">
        </div>
    </div>

    <div class="section">
        <h3>页数控制</h3>
        <p class="description">每页60条数据，页数从0开始，即第一页是0</p>
        <div class="radio-group">
            <label>
                <input type="radio" name="downloadType" value="specific"> 指定页数
            </label>
            <label>
                <input type="radio" name="downloadType" value="all" checked> 全部
            </label>
        </div>
        <div id="pageControl">
            <div class="input-group">
                <label>开始页数：</label>
                <input type="number" id="startPage" value="0" min="0">
            </div>
            <div class="input-group">
                <label>结束页数：</label>
                <input type="number" id="endPage" value="2" min="0">
            </div>
        </div>
    </div>

    <div class="section" style="display: flex; align-items: center; gap: 10px; background: transparent; border: none; box-shadow: none;">
        <button onclick="downloadData()">下载</button>
        <span id="downloadStatus"></span>
    </div>
    <div id="message"></div>

    <script>
        // 设置日期选择器的默认值为今天
        const today = new Date();
        document.getElementById('startDate').valueAsDate = today;
        document.getElementById('endDate').valueAsDate = today;

        // 页面加载时隐藏页数控制
        document.getElementById('pageControl').classList.add('hidden');

        // 日期类型选择处理
        document.getElementsByName('dateType').forEach(radio => {
            radio.addEventListener('change', function() {
                document.getElementById('dateRangeControl').classList.toggle('hidden', this.value === 'realtime');
            });
        });

        // 下载类型选择处理
        document.getElementsByName('downloadType').forEach(radio => {
            radio.addEventListener('change', function() {
                document.getElementById('pageControl').classList.toggle('hidden', this.value === 'all');
            });
        });

        function downloadData() {
            // 验证输入
            const rStart = document.getElementById('rStart').value;
            const rEnd = document.getElementById('rEnd').value;
            const downloadType = document.querySelector('input[name="downloadType"]:checked').value;
            const startPage = document.getElementById('startPage').value;
            const endPage = document.getElementById('endPage').value;

            // 清除上一次的下载完成提示
            const statusElement = document.getElementById('downloadStatus');
            const messageDiv = document.getElementById('message');
            statusElement.textContent = '';
            messageDiv.textContent = '';
            messageDiv.className = '';

            // 显示开始下载状态
            statusElement.textContent = '开始下载...';

            if (!rStart || !rEnd) {
                showMessage('请填写开始时间和结束时间', true);
                return;
            }

            if (downloadType === 'specific' && (!startPage || !endPage)) {
                showMessage('请填写开始页数和结束页数', true);
                return;
            }

            const dateType = document.querySelector('input[name="dateType"]:checked').value;
            let startDate = '';
            let endDate = '';
            
            if (dateType === 'history') {
                startDate = document.getElementById('startDate').value;
                endDate = document.getElementById('endDate').value;
                
                if (!startDate || !endDate) {
                    showMessage('请选择开始日期和结束日期', true);
                    return;
                }
                
                if (startDate > endDate) {
                    showMessage('开始日期不能大于结束日期', true);
                    return;
                }
            }

            const requestData = {
                dateType: dateType,
                startDate: startDate,
                endDate: endDate,
                rStart: rStart,
                rEnd: rEnd,
                downloadType: downloadType,
                startPage: startPage,
                endPage: endPage
            };

            console.log('发送请求数据:', requestData);

            // 发送请求
            fetch('/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('收到响应:', data);
                // 显示完成状态
                statusElement.textContent = data.success ? 
                    `下载完成，共获取${data.total_records}条数据` : '';
                showMessage(data.message, !data.success);
            })
            .catch(error => {
                console.error('下载错误:', error);
                statusElement.textContent = '';
                showMessage('下载失败：' + error, true);
            });
        }

        function showMessage(message, isError) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = isError ? 'error' : '';
        }
    </script>
</body>
</html> 