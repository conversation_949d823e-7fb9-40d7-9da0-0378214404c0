import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
import pythoncom
import pyWinhook
import os
from datetime import datetime
import time
import threading
import sys
import io
import re
import win32process
import psutil
from ctypes import *
from tkinter import messagebox
import pytesseract

# 设置 Tesseract 的路径
pytesseract.pytesseract.tesseract_cmd = r'F:\Tesseract-OCR\tesseract.exe'

# 同花顺通信相关常量
FAGE_READWRITE = 0x04
PROCESS_ALL_ACCESS = 0x001F0FFF
VIRTUAL_MEN = (0x1000 | 0x2000)

# 全局变量
running = True
kernel32 = windll.kernel32
user32 = windll.user32
IsWindowVisible = user32.IsWindowVisible

def get_pids(pname):
    pids = []
    for proc in psutil.process_iter():
        if pname in proc.name():
            pids.append(proc.pid)
    return pids

def get_handles(pid):
    def callback(hwnd, handles):
        if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindowEnabled(hwnd):
            _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
            if found_pid == pid:
                handles.append(hwnd)
            return True
    handles = []
    win32gui.EnumWindows(callback, handles)
    return handles

def get_handle(pname):
    pids = get_pids(pname)
    for pid in pids:
        handles = get_handles(pid)
        for hwnd in handles:
            if IsWindowVisible(hwnd):
                return hwnd

def ths_prc_hwnd():
    pl = psutil.pids()
    for pid in pl:
        try:
            if psutil.Process(pid).name().lower() == 'hexin.exe':
                if isinstance(pid, int):
                    ths_process_hwnd = kernel32.OpenProcess(PROCESS_ALL_ACCESS, False, int(pid))
                    return ths_process_hwnd
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def bytes_16(dec_num, code):
    ascii_char = chr(dec_num)
    codex = ascii_char + str(code)
    bytes_codex = codex.encode('ascii', 'ignore')
    return bytes_codex

def ths_convert_code(code):
    if str(code)[0] == '6':
        dec_num = int('11', 16)
        bytes_codex = bytes_16(dec_num, code)
    elif str(code).startswith('11'):
        dec_num = int('13', 16)
        bytes_codex = bytes_16(dec_num, code)
    elif str(code).startswith('12'):
        dec_num = int('23', 16)
        bytes_codex = bytes_16(dec_num, code)
    else:
        dec_num = int('21', 16)
        bytes_codex = bytes_16(dec_num, code)
    return bytes_codex

def send_to_ths(stock_code):
    """
    发送股票代码到同花顺
    """
    try:
        ths_process_hwnd = ths_prc_hwnd()
        if not ths_process_hwnd:
            print("未找到同花顺进程，请确保同花顺已经打开")
            return
            
        argv_address = kernel32.VirtualAllocEx(ths_process_hwnd, 0, 8, VIRTUAL_MEN, FAGE_READWRITE)
        bytes_str = ths_convert_code(stock_code)
        kernel32.WriteProcessMemory(ths_process_hwnd, argv_address, bytes_str, 7, None)
        ths_handle = get_handle('hexin.exe')
        if not ths_handle:
            print("未找到同花顺窗口，请确保同花顺已经打开")
            return
        win32api.SendMessage(ths_handle, int(1168), 0, argv_address)
        print(f"已发送股票代码 {stock_code} 到同花顺")
    except Exception as e:
        print(f"发送股票代码时出错: {str(e)}")

def extract_six_digits(text):
    # 查找连续的6位数字
    pattern = r'\d{6}'
    matches = re.findall(pattern, text)
    return matches[0] if matches else None

def recognize_text(image):
    try:
        # 使用 Tesseract 进行 OCR 识别
        text = pytesseract.image_to_string(image, lang='eng')
        
        # 查找6位数字
        digits = extract_six_digits(text)
        if digits:
            print(f"识别到6位数字: {digits}")
            # 发送股票代码到同花顺
            send_to_ths(digits)
        else:
            print("未找到6位数字")
        print(f"识别到的完整文本: {text}")
        print("-" * 50)
    except Exception as e:
        print(f"OCR识别出错: {str(e)}")

def cleanup_and_exit():
    global running
    running = False
    if 'hm' in globals():
        win32gui.PostQuitMessage(0)
    sys.exit(0)

def take_screenshot(window_handle):
    try:
        # 获取窗口大小
        left, top, right, bottom = win32gui.GetWindowRect(window_handle)
        window_width = right - left
        
        # 计算截图区域
        capture_height = 70
        capture_width = 160
        x_offset = (window_width - capture_width) // 2
        
        # 创建设备上下文
        hwndDC = win32gui.GetWindowDC(window_handle)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        
        # 创建位图对象
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, capture_width, capture_height)
        saveDC.SelectObject(saveBitMap)
        
        # 复制指定区域的窗口内容到位图
        saveDC.BitBlt(
            (0, 0),
            (capture_width, capture_height),
            mfcDC,
            (x_offset, 0),
            win32con.SRCCOPY
        )
        
        # 将位图转换为字节流
        bmpinfo = saveBitMap.GetInfo()
        bmpstr = saveBitMap.GetBitmapBits(True)
        
        # 创建PIL Image对象
        image = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        
        # 清理GDI资源
        win32gui.DeleteObject(saveBitMap.GetHandle())
        saveDC.DeleteDC()
        mfcDC.DeleteDC()
        win32gui.ReleaseDC(window_handle, hwndDC)
        
        # 识别文字
        recognize_text(image)
        
    except Exception as e:
        print(f"截图过程中出现错误: {str(e)}")

def process_click(window_handle):
    time.sleep(1.0)  # 延迟1秒后截图
    if running:  # 检查程序是否仍在运行
        take_screenshot(window_handle)

def on_mouse_event(event):
    global running
    if not running:
        return True
        
    if event.MessageName == "mouse left down":
        window_handle = win32gui.WindowFromPoint((event.Position[0], event.Position[1]))
        window_title = win32gui.GetWindowText(window_handle)
        print(f"\n捕获到窗口: {window_title}")
        print("等待1秒后开始截图...")
        
        # 创建新线程进行截图
        screenshot_thread = threading.Thread(target=process_click, args=(window_handle,))
        screenshot_thread.daemon = True
        screenshot_thread.start()
        
    return True

def main():
    global running
    try:
        # 检查Tesseract是否可用
        try:
            pytesseract.get_tesseract_version()
            print("Tesseract OCR 初始化成功")
        except Exception as e:
            print(f"Tesseract OCR 初始化失败: {e}")
            print("请确保已安装Tesseract，并且路径设置正确")
            return
            
        # 创建鼠标钩子
        global hm
        hm = pyWinhook.HookManager()
        hm.MouseAll = on_mouse_event
        hm.HookMouse()
        
        print("程序已启动，请点击要截图的窗口...")
        print("按Ctrl+C可以退出程序")
        
        # 开始消息循环
        pythoncom.PumpMessages()
    except KeyboardInterrupt:
        print("\n程序被用户终止")
    except Exception as e:
        print(f"程序出现错误: {str(e)}")
    finally:
        cleanup_and_exit()

if __name__ == "__main__":
    main()
