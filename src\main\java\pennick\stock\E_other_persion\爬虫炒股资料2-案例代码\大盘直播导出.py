import requests
from datetime import datetime
from docx import Document
from docx.shared import RGBColor
import os
# 指定保存路径
save_path = r"D:\01\其他项目\临时归档\定制-开盘啦大盘直播 晚上导出"


url = "https://api.fupanwang.com/kpl/zhibo?"
if not os.path.exists(save_path):
    os.makedirs(save_path)

try:
    response = requests.get(url)
    response.raise_for_status()
    data = response.json()

    # 检查是否存在所需字段
    if "data" in data and "info" in data["data"] and "List" in data["data"]["info"]:
        # 获取 List 字段的数据
        item_list = data["data"]["info"]["List"]
        # 按照 Time 字段进行正序排序
        sorted_list = sorted(item_list, key=lambda x: x["Time"])

        # 创建 Word 文档
        doc = Document()

        for item in sorted_list:
            timestamp = item["Time"]
            time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M")
            comment = item["Comment"]
            stock = item["Stock"]

            time_paragraph = doc.add_paragraph("Time: ")
            run = time_paragraph.add_run(time_str)
            run.bold = True
            # run.font.color.rgb = RGBColor(255, 0, 0)

            # 添加评论到文档
            doc.add_paragraph(f"Comment: {comment}")

            # 处理股票信息
            stock_paragraph = doc.add_paragraph("Stock: ")
            for index, stock_item in enumerate(stock):
                code, name, value = stock_item
                # 添加股票代码和名称
                stock_paragraph.add_run(f"[{code}, {name}, ")
                # 根据值的正负设置颜色
                run = stock_paragraph.add_run(str(value))
                if value > 0:
                    run.font.color.rgb = RGBColor(255, 0, 0)  # 红色
                elif value < 0:
                    run.font.color.rgb = RGBColor(56, 102, 65)  # 绿色
                stock_paragraph.add_run("] ")

                # 每两个元素换行
                if (index + 1) % 2 == 0 and index != len(stock) - 1:
                    stock_paragraph.add_run("\n")

            # 添加分隔线
            doc.add_paragraph("-" * 50)

        # 获取当前日期作为文件名
        current_date = datetime.now().strftime("%Y-%m-%d")
        file_name = f"{current_date}.docx"
        # 拼接完整的文件路径
        full_path = os.path.join(save_path, file_name)

        # 保存 Word 文档
        doc.save(full_path)
        print(f"数据已成功保存到 {full_path}")
    else:
        print("数据中缺少所需字段。")

except requests.exceptions.RequestException as e:
    print(f"请求发生错误: {e}")
except KeyError as e:
    print(f"解析数据时发生键错误: {e}")
except Exception as e:
    print(f"发生未知错误: {e}")