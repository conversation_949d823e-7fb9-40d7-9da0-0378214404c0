from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import pandas as pd
from bs4 import BeautifulSoup
import openpyxl
from datetime import datetime
from selenium.webdriver.chrome.options import Options

current_date = datetime.now().strftime('%Y-%m-%d')
# current_date = '2025-02-25'
url = f'https://www.jiuyangongshe.com/action/{current_date}'
# url = f'https://www.jiuyangongshe.com/action/2024-10-30'
#url = f'https://www.jiuyangongshe.com/action/{current_date}'
# 定义函数：查找第一个Excel文件
# 查找脚本所在文件夹中的第一个Excel文件
def find_first_excel_file(directory):
    for filename in os.listdir(directory):
        if filename.endswith('.xlsx'):
            return os.path.join(directory, filename)
    return None

# 定义函数：删除"sm"或"sz"
# 删除单元格中包含的"sm"或"sz"
def remove_sm_sz(cell):
    if cell.value and isinstance(cell.value, str):
        cell.value = cell.value.replace("sh", "").replace("sz", "").replace("bj", "")

# 复制第7列中"1、"前面的文本到第6列
def copy_text_before_marker(sheet):
    for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, max_col=sheet.max_column, values_only=False):
        # 获取第7列（索引为6）的单元格内容
        cell_value = row[4].value
        if cell_value and isinstance(cell_value, str) and "1、" in cell_value:
            # 提取"1、"前面的文本
            text_before_marker = cell_value.split("1、")[0].strip()
            # 复制到第6列（索引为5）的单元格中
            row[3].value = text_before_marker

# 删除第7列中"1、"前面的文本
def delete_text_before_marker(sheet):
    for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, max_col=sheet.max_column, values_only=False):
        cell = row[4]  # 修改索引从4到6以指向第7列
        cell_value = cell.value
        if cell_value and isinstance(cell_value, str) and "1、" in cell_value:
            split_index = cell_value.find("1、")  # 查找"1、"的位置
            if split_index != -1:  # 如果找到了"1、"
                text_after_marker = cell_value[split_index:].strip()  # 直接从"1、"开始取字符串
                cell.value = text_after_marker  # 更新单元格的值

# 设置 Chrome 选项
chrome_options = Options()
chrome_options.add_argument("--head")  # 无头模式
# chrome_options.add_argument("--head")  # 无头模式
chrome_options.add_argument("--disable-gpu")  # 禁用 GPU 加速

# 输出消息：启动 Chrome 浏览器
print("Starting Chrome browser...")
# 启动 Chrome 浏览器
driver = webdriver.Chrome(options=chrome_options)

# 输出消息：访问网页
print("Accessing the webpage...")
# 访问网页
driver.get(url)

# 输出消息：等待动态加载完成
print("Waiting for dynamic content to load...")
# 等待动态加载完成（根据具体情况调整等待时间）
driver.implicitly_wait(10)

try:
    print("***********")
    print("eight986")

    # 等待账号密码登录选项加载并点击
    wait = WebDriverWait(driver, 10)
    print("等待点击元素: //div[@id='tab-accounts']")
    account_tab = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='tab-accounts']")))
    account_tab.click()
    print("找到并点击元素: //div[@id='tab-accounts']")

    # 等待输入框加载
    print("切换到账号密码登录选项后，等待输入框加载...")
    time.sleep(3)  # 等待输入框加载



    # 倒计时 15 秒
    for i in range(15, 0, -1):
        print(f"倒计时 {i} 秒，手动点击登录按钮")
        time.sleep(1)

    print("倒计时结束！")

    # 模拟点击全部异动解析标签
    try:
        driver.find_element(By.XPATH, "//div[text()='全部异动解析']").click()
    except Exception as e:
        print("无法找到全部异动解析标签:", e)

    # 输出消息：等待动态加载完成
    print("Waiting for dynamic content to load...")
    time.sleep(3)  # 等待3秒

    # 输出消息：获取页面源代码
    print("Getting page source...")
    page_source = driver.page_source

finally:
    # 关闭浏览器
    driver.quit()

    # 输出消息：保存页面源代码到本地文件
    print("Saving page source to a local file...")
    with open("page_source.html", "w", encoding="utf-8") as file:
        file.write(page_source)

# 获取脚本所在目录的路径
script_dir = os.path.dirname(os.path.abspath(__file__))

# 设置HTML文件所在文件夹路径和输出的Excel文件名
folder_path = script_dir

# 读取HTML文件
with open('page_source.html', 'r', encoding='utf-8') as file:
    html_content = file.read()

# 创建BeautifulSoup对象
soup = BeautifulSoup(html_content, 'html.parser')

# 输出消息：提取数据
print("Extracting data from the webpage...")
data = []
modules = soup.find_all('li', class_='module')
for module in modules:
    module_name_element = module.find('div', class_='fs18-bold lf')
    module_number_element = module.find('div', class_='number lf')
    if module_name_element:
        module_name = module_name_element.text.strip()
        module_number = module_number_element.text.strip()
        module_name_module_number = f"{module_name}({module_number})"
        stocks = module.find_all('div', class_='table-box')
        for stock in stocks:
            stock_names = stock.find_all('div', class_='shrink fs15-bold')
            stock_codes = stock.find_all('div', class_='shrink fs12-bold-ash force-wrap')
            latest_prices = stock.find_all('div', class_='shrink number')
            change_percents = stock.find_all('div', class_='shrink cred')
            trading_times = stock.find_all('div', class_='shrink fs15')
            analyses = stock.find_all('pre', class_='pre tl hilll')
            for i in range(len(stock_names)):
                stock_name = stock_names[i].text.strip()
                stock_code = stock_codes[i].text.strip()
                latest_price = latest_prices[i].text.strip()
                change_percent = change_percents[i].text.strip() if change_percents and len(change_percents) > i else "N/A"
                trading_time = trading_times[i].text.strip()
                analysis = analyses[i].text.strip()
                data.append([stock_code, stock_name, module_name_module_number, analysis])
    else:
        print("模块名称未找到")

# 输出消息：创建DataFrame并写入Excel文件
print("Creating DataFrame and writing to Excel file...")
df = pd.DataFrame(data, columns=['股票代码', '股票名称', '板块', '原因详解'])

# 生成带有空格和JY后缀的文件名
excel_file = f"{current_date} 韭研.xlsx"
excel_file_path = os.path.join(folder_path, excel_file)

# 将数据写入Excel文件
df.to_excel(excel_file_path, index=False)

# 输出消息：查找第一个Excel文件
print("Finding the first Excel file...")
excel_file_path = find_first_excel_file(folder_path)

if excel_file_path:
    print(f"Found Excel file: {excel_file_path}")
    workbook = openpyxl.load_workbook(excel_file_path)
    sheet = workbook.active

    print("Inserting blank column...")
    sheet.insert_cols(4)

    print("Removing 'sm' or 'sz'...")
    for row in sheet.iter_rows(min_row=1, max_col=sheet.max_column, values_only=False):
        cell = row[0]  # 第二列单元格
        remove_sm_sz(cell)

    print("Copying text...")
    copy_text_before_marker(sheet)

    print("Deleting text...")
    delete_text_before_marker(sheet)

    print("Saving the modified workbook...")
    workbook.save(excel_file_path)
    print("Excel file updated successfully.")
else:
    print("No Excel file found in the script directory.")
