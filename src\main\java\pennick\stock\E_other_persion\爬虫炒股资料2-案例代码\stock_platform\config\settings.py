"""
股票数据平台配置文件
"""
import os
from pathlib import Path
from datetime import datetime

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL", f"sqlite:///{BASE_DIR}/data/stock_data.db")

# 数据源配置
DATA_SOURCES = {
    "jiuyan": {
        "name": "韭研数据",
        "base_url": "https://www.jiuyangongshe.com",
        "login_required": True,
        "rate_limit": 1,  # 秒
        "description": "股票异动解析数据"
    },
    "kaipanla": {
        "name": "开盘啦数据", 
        "base_url": "https://apphis.longhuvip.com",
        "rate_limit": 0.5,
        "description": "涨停、跌停、竞价等数据",
        "data_types": ["曾跌停", "跌停", "竞价", "炸板", "涨停", "自然涨停"]
    },
    "longhubang": {
        "name": "龙虎榜数据",
        "base_url": "https://applhb.longhuvip.com",
        "rate_limit": 0.5,
        "description": "营业部交易数据"
    }
}

# 文件路径配置
DATA_DIR = BASE_DIR / "data"
LOG_DIR = BASE_DIR / "logs"
EXPORT_DIR = DATA_DIR / "exports"
RAW_DATA_DIR = DATA_DIR / "raw"
PROCESSED_DATA_DIR = DATA_DIR / "processed"

# Web配置
WEB_CONFIG = {
    "host": "127.0.0.1",
    "port": 8000,
    "debug": True
}

# 创建必要目录
def create_directories():
    """创建必要的目录"""
    directories = [
        DATA_DIR, LOG_DIR, EXPORT_DIR, 
        RAW_DATA_DIR, PROCESSED_DATA_DIR,
        BASE_DIR / "web" / "static",
        BASE_DIR / "web" / "templates"
    ]
    
    for dir_path in directories:
        dir_path.mkdir(parents=True, exist_ok=True)
        
    print("项目目录结构创建完成")

if __name__ == "__main__":
    create_directories()
