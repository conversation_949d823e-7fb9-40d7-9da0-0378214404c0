import pandas as pd
from pandas_market_calendars import get_calendar
from datetime import datetime, timedelta
import requests
from openpyxl import Workbook
from openpyxl.styles import PatternFill


sh_exchange_calendar = get_calendar('XSHG')

# 设置起始日期和结束日期
start_date = '20241009'
end_date = '20250316'

# 获取起始日期到结束日期之间的所有交易日
trading_days = sh_exchange_calendar.valid_days(start_date=start_date, end_date=end_date)

# 将交易日日期存储到列表中，并转换为指定格式的字符串
dates_list = [date.strftime('%Y-%m-%d') for date in trading_days]

# ---- 第二部分：请求数据并生成 Excel 文件 ----
# 遍历交易日列表
for date_value in dates_list:
    # 构建 API URL
    url_base = f'https://apphis.longhuvip.com/w1/api/index.php?Filter=0&FilterGem=0&FilterMotherboard=0&FilterTIB=0&Index=0&Is_st=0&Order=1&PhoneOSNew=2&PidType=8&Type=6&VerSion=*******&a=HisDaBanList&apiv=w32&c=HisHomeDingPan&st=60&Day={date_value}'

    # 初始化变量以存储所有数据
    all_data = []

    # 初始化索引值
    index = 0

    # 循环发送请求，直到获取所有数据为止
    while True:
        url = url_base + f'&Index={index}'
        try:
            response = requests.get(url).json()
        except requests.exceptions.RequestException as e:
            print(f"请求日期 {date_value} 失败: {e}")
            break
        except ValueError:
            print(f"日期 {date_value} 的响应内容不是有效的JSON")
            break

        if 'list' in response:
            data = response['list']
            if not data:
                # 如果没有更多数据，退出循环
                break
            else:
                all_data.extend(data)
                # 更新索引值以获取下一页数据
                index += 60
        else:
            print(f"日期 {date_value} 的 JSON 数据中不存在 'list' 键")
            break

    # 按照第 18 个元素的值从大到小进行排序 (竞价委买_亿)
    try:
        sorted_data = sorted(all_data, key=lambda x: x[18], reverse=True)
    except IndexError as e:
        print(f"日期 {date_value} 排序时发生错误: {e}")
        sorted_data = all_data  # 如果排序失败，保留原始顺序

    # 创建一个Excel工作簿
    wb = Workbook()
    ws = wb.active

    # 添加表头
    header = ["名称", "代码", "竞价涨幅", "实时涨幅", "板块", "流通值_亿", "涨停委买_亿", "竞价净额_万", "竞价换手", "竞价成交_万"]
    ws.append(header)

    # 写入数据到工作表，只选择特定列的数据，并计算比值
    selected_columns = [1, 0, 19, 4, 11, 15, 18, 20, 21, 22]
    for row_data in sorted_data:
        try:
            selected_data = [row_data[i] for i in selected_columns]
        except IndexError as e:
            print(f"日期 {date_value} 数据行索引错误: {e}，跳过该行")
            continue

        # 如果selected_columns中的值中包含"ST"，则跳过该行数据
        if any("ST" in str(value) for value in selected_data):
            continue

        # 对特定列进行数值处理
        try:
            selected_data[5] = round(selected_data[5] / 100000000, 2)  # 将值除以 100,000,000 并保留两位小数
            selected_data[6] = round(selected_data[6] / 100000000, 2)  # 将值除以 100,000,000 并保留两位小数
            selected_data[7] = round(selected_data[7] / 10000, 2)       # 将值除以 10,000 并保留两位小数
            selected_data[9] = round(selected_data[9] / 10000, 2)       # 将值除以 10,000 并保留两位小数
        except (TypeError, ValueError) as e:
            print(f"日期 {date_value} 数据处理错误: {e}，跳过该行")
            continue

        # 再次检查是否包含"ST"，以防数据处理后引入新的"ST"
        if not any("ST" in str(value) for value in selected_data):
            ws.append(selected_data)

    # 格式化文件名，确保不包含无效字符
    file_name = f"{date_value}.xlsx"

    try:
        wb.save(file_name)
        print(f"日期 {date_value} 的文件已保存: {file_name}")
    except OSError as e:
        print(f"保存日期 {date_value} 的文件时出错: {e}")

print("所有日期的数据处理完成。")