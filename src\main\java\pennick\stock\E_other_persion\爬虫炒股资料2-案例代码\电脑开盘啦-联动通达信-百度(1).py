import win32gui
import win32ui
import win32con
import win32api
from PIL import Image
import pythoncom
import pyWinhook
import os
from datetime import datetime
import time
import threading
import sys
import io
import base64
import requests
import re

# 百度 OCR API 配置
API_KEY = "uiDqeXy8KdKL替换自己的百度ocr"
SECRET_KEY = "Y9L4DX4EoIlqOtlA0M替换自己的百度ocr"
OCR_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic"
TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token"

# 全局变量
running = True
access_token = None

def broadcast_stock_code(stock_code):
    """
    广播股票代码到通达信
    """
    try:
        if stock_code.startswith(('0', '3')):
            codex = '6' + stock_code
        elif stock_code.startswith('6'):
            codex = '7' + stock_code
        else:
            codex = '4' + stock_code

        UWM_STOCK = win32api.RegisterWindowMessage('stock')
        win32gui.PostMessage(win32con.HWND_BROADCAST, UWM_STOCK, int(codex), 0)
        print(f"已发送股票代码 {stock_code} 到通达信")
    except Exception as e:
        print(f"发送股票代码时出错: {str(e)}")

def get_access_token():
    data = {
        "grant_type": "client_credentials",
        "client_id": API_KEY,
        "client_secret": SECRET_KEY,
    }
    response = requests.post(TOKEN_URL, data=data)
    if response.status_code == 200:
        return response.json().get("access_token")
    else:
        raise Exception(f"获取access token失败: {response.text}")

def extract_six_digits(text):
    # 查找连续的6位数字
    pattern = r'\d{6}'
    matches = re.findall(pattern, text)
    return matches[0] if matches else None

def recognize_text(image_bytes):
    global access_token
    
    # 将图片转换为base64编码
    image_base64 = base64.b64encode(image_bytes).decode("utf-8")
    
    # 请求参数
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    data = {
        "image": image_base64,
        "access_token": access_token,
    }
    
    # 发送请求
    response = requests.post(OCR_URL, headers=headers, data=data)
    if response.status_code == 200:
        result = response.json()
        if "words_result" in result:
            # 提取所有识别到的文本
            text = " ".join([item["words"] for item in result["words_result"]])
            # 查找6位数字
            digits = extract_six_digits(text)
            if digits:
                print(f"识别到6位数字: {digits}")
                # 发送股票代码到通达信
                broadcast_stock_code(digits)
            else:
                print("未找到6位数字")
            print(f"识别到的完整文本: {text}")
            print("-" * 50)
    else:
        print(f"OCR请求失败: {response.text}")

def cleanup_and_exit():
    global running
    running = False
    if 'hm' in globals():
        win32gui.PostQuitMessage(0)
    sys.exit(0)

def take_screenshot(window_handle):
    try:
        # 获取窗口大小
        left, top, right, bottom = win32gui.GetWindowRect(window_handle)
        window_width = right - left
        
        # 计算截图区域
        capture_height = 70
        capture_width = 160
        x_offset = (window_width - capture_width) // 2
        
        # 创建设备上下文
        hwndDC = win32gui.GetWindowDC(window_handle)
        mfcDC = win32ui.CreateDCFromHandle(hwndDC)
        saveDC = mfcDC.CreateCompatibleDC()
        
        # 创建位图对象
        saveBitMap = win32ui.CreateBitmap()
        saveBitMap.CreateCompatibleBitmap(mfcDC, capture_width, capture_height)
        saveDC.SelectObject(saveBitMap)
        
        # 复制指定区域的窗口内容到位图
        saveDC.BitBlt(
            (0, 0),
            (capture_width, capture_height),
            mfcDC,
            (x_offset, 0),
            win32con.SRCCOPY
        )
        
        # 将位图转换为字节流
        bmpinfo = saveBitMap.GetInfo()
        bmpstr = saveBitMap.GetBitmapBits(True)
        
        # 创建PIL Image对象
        image = Image.frombuffer(
            'RGB',
            (bmpinfo['bmWidth'], bmpinfo['bmHeight']),
            bmpstr, 'raw', 'BGRX', 0, 1
        )
        
        # 将图片转换为字节流
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        img_byte_arr = img_byte_arr.getvalue()
        
        # 清理GDI资源
        win32gui.DeleteObject(saveBitMap.GetHandle())
        saveDC.DeleteDC()
        mfcDC.DeleteDC()
        win32gui.ReleaseDC(window_handle, hwndDC)
        
        # 识别文字
        recognize_text(img_byte_arr)
        
    except Exception as e:
        print(f"截图过程中出现错误: {str(e)}")

def process_click(window_handle):
    time.sleep(1.0)  # 延迟1.5秒后截图
    if running:  # 检查程序是否仍在运行
        take_screenshot(window_handle)

def on_mouse_event(event):
    global running
    if not running:
        return True
        
    if event.MessageName == "mouse left down":
        window_handle = win32gui.WindowFromPoint((event.Position[0], event.Position[1]))
        window_title = win32gui.GetWindowText(window_handle)
        print(f"\n捕获到窗口: {window_title}")
        print("等待1.5秒后开始截图...")
        
        # 创建新线程进行截图
        screenshot_thread = threading.Thread(target=process_click, args=(window_handle,))
        screenshot_thread.daemon = True
        screenshot_thread.start()
        
    return True

def main():
    global running, access_token
    try:
        # 获取百度OCR的access token
        print("正在获取百度OCR访问令牌...")
        access_token = get_access_token()
        print("访问令牌获取成功")
        
        # 创建鼠标钩子
        global hm
        hm = pyWinhook.HookManager()
        hm.MouseAll = on_mouse_event
        hm.HookMouse()
        
        print("程序已启动，请点击要截图的窗口...")
        print("按Ctrl+C可以退出程序")
        
        # 开始消息循环
        pythoncom.PumpMessages()
    except KeyboardInterrupt:
        print("\n程序被用户终止")
    except Exception as e:
        print(f"程序出现错误: {str(e)}")
    finally:
        cleanup_and_exit()

if __name__ == "__main__":
    main()
