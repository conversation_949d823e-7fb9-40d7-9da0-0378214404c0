#!/usr/bin/env python3
"""
股票数据平台简化启动脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_minimal_web_app():
    """创建最小化的Web应用"""
    try:
        from flask import Flask, render_template_string, jsonify
        
        app = Flask(__name__)
        
        # 主页HTML模板
        INDEX_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票数据管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .crawler-card {
            transition: transform 0.2s;
            cursor: pointer;
            height: 200px;
        }
        .crawler-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    📈 股票数据管理平台
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <span class="status-indicator"></span>
                        系统运行中
                    </span>
                </div>
            </div>
        </nav>

        <!-- 欢迎信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-success" role="alert">
                    <h4 class="alert-heading">🎉 平台启动成功！</h4>
                    <p>股票数据管理平台已成功启动，您可以点击下方的模块卡片来执行相应的数据采集功能。</p>
                    <hr>
                    <p class="mb-0">💡 提示：点击任意模块卡片即可开始使用对应的数据采集功能。</p>
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card crawler-card h-100" onclick="showInfo('jiuyan')">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <span class="status-indicator"></span>
                            📊 韭研数据
                        </h5>
                        <p class="card-text flex-grow-1">
                            获取股票异动解析数据<br>
                            <small class="text-muted">需要手动登录</small>
                        </p>
                        <div class="mt-auto">
                            <span class="badge bg-primary">异动解析</span>
                            <span class="badge bg-secondary">手动登录</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card crawler-card h-100" onclick="showInfo('kaipanla')">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <span class="status-indicator"></span>
                            📈 开盘啦数据
                        </h5>
                        <p class="card-text flex-grow-1">
                            涨停、跌停、竞价等数据<br>
                            <small class="text-muted">支持6种数据类型</small>
                        </p>
                        <div class="mt-auto">
                            <span class="badge bg-success">自动获取</span>
                            <span class="badge bg-info">多类型</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card crawler-card h-100" onclick="showInfo('longhubang')">
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <span class="status-indicator"></span>
                            🏢 龙虎榜数据
                        </h5>
                        <p class="card-text flex-grow-1">
                            营业部交易数据<br>
                            <small class="text-muted">支持多个营业部</small>
                        </p>
                        <div class="mt-auto">
                            <span class="badge bg-warning">营业部</span>
                            <span class="badge bg-dark">交易数据</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🚀 快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-success w-100" onclick="showInfo('demo')">
                                    📋 查看演示
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-info w-100" onclick="showInfo('help')">
                                    ❓ 使用帮助
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-warning w-100" onclick="showInfo('files')">
                                    📁 文件管理
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary w-100" onclick="showInfo('status')">
                                    📊 系统状态
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息显示区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 id="infoTitle">💡 使用说明</h5>
                    </div>
                    <div class="card-body">
                        <div id="infoContent">
                            <p><strong>欢迎使用股票数据管理平台！</strong></p>
                            <p>这是一个统一的股票数据采集和管理系统，整合了多个数据源：</p>
                            <ul>
                                <li><strong>韭研数据</strong>：股票异动解析数据</li>
                                <li><strong>开盘啦数据</strong>：涨停、跌停、竞价等6种数据类型</li>
                                <li><strong>龙虎榜数据</strong>：营业部交易数据</li>
                            </ul>
                            <p><strong>使用方法：</strong></p>
                            <ol>
                                <li>点击上方的模块卡片选择数据源</li>
                                <li>根据提示配置参数</li>
                                <li>执行数据采集</li>
                                <li>查看和下载结果文件</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showInfo(type) {
            const title = document.getElementById('infoTitle');
            const content = document.getElementById('infoContent');
            
            switch(type) {
                case 'jiuyan':
                    title.textContent = '📊 韭研数据说明';
                    content.innerHTML = `
                        <h6>功能说明：</h6>
                        <p>获取韭研网站的股票异动解析数据，包括股票代码、名称、板块和详细分析。</p>
                        <h6>使用方法：</h6>
                        <ol>
                            <li>系统会自动打开浏览器</li>
                            <li>手动登录韭研网站</li>
                            <li>等待15秒倒计时</li>
                            <li>系统自动获取数据并导出</li>
                        </ol>
                        <div class="alert alert-warning">
                            <strong>注意：</strong>需要有韭研网站的账号，并且需要手动登录。
                        </div>
                    `;
                    break;
                case 'kaipanla':
                    title.textContent = '📈 开盘啦数据说明';
                    content.innerHTML = `
                        <h6>支持的数据类型：</h6>
                        <ul>
                            <li>涨停：当日涨停股票</li>
                            <li>跌停：当日跌停股票</li>
                            <li>竞价：集合竞价数据</li>
                            <li>炸板：涨停后打开的股票</li>
                            <li>曾跌停：曾经跌停的股票</li>
                            <li>自然涨停：自然涨停股票</li>
                        </ul>
                        <div class="alert alert-success">
                            <strong>优势：</strong>无需登录，自动获取，支持批量下载。
                        </div>
                    `;
                    break;
                case 'longhubang':
                    title.textContent = '🏢 龙虎榜数据说明';
                    content.innerHTML = `
                        <h6>功能说明：</h6>
                        <p>获取指定营业部的龙虎榜交易数据，包括买卖金额、净买入等信息。</p>
                        <h6>支持的营业部：</h6>
                        <ul>
                            <li>上塘路营业部</li>
                            <li>养家营业部</li>
                        </ul>
                        <h6>数据内容：</h6>
                        <ul>
                            <li>股票代码和名称</li>
                            <li>买入金额（万元）</li>
                            <li>卖出金额（万元）</li>
                            <li>净买入金额（万元）</li>
                        </ul>
                    `;
                    break;
                case 'demo':
                    title.textContent = '📋 演示说明';
                    content.innerHTML = `
                        <h6>如何运行演示：</h6>
                        <p>在命令行中运行以下命令查看平台演示：</p>
                        <pre class="bg-light p-2"><code>python demo.py</code></pre>
                        <p>演示将展示：</p>
                        <ul>
                            <li>平台功能概览</li>
                            <li>数据处理演示</li>
                            <li>文件导出演示</li>
                            <li>项目结构说明</li>
                        </ul>
                    `;
                    break;
                case 'help':
                    title.textContent = '❓ 使用帮助';
                    content.innerHTML = `
                        <h6>常见问题：</h6>
                        <div class="accordion" id="helpAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#help1">
                                        如何安装依赖？
                                    </button>
                                </h2>
                                <div id="help1" class="accordion-collapse collapse show">
                                    <div class="accordion-body">
                                        运行命令：<code>pip install flask requests beautifulsoup4 pandas openpyxl selenium</code>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#help2">
                                        数据保存在哪里？
                                    </button>
                                </h2>
                                <div id="help2" class="accordion-collapse collapse">
                                    <div class="accordion-body">
                                        所有导出的数据保存在 <code>data/exports/</code> 目录中，格式为Excel文件。
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'files':
                    title.textContent = '📁 文件管理';
                    content.innerHTML = `
                        <h6>文件存储结构：</h6>
                        <pre class="bg-light p-2"><code>data/
├── raw/         # 原始数据
├── processed/   # 处理后数据
└── exports/     # 导出文件 ⭐</code></pre>
                        <p>所有的Excel导出文件都保存在 <code>data/exports/</code> 目录中。</p>
                        <p>文件命名格式：<code>[数据源]_[时间戳].xlsx</code></p>
                        <div class="alert alert-info">
                            <strong>提示：</strong>可以直接在文件管理器中打开 exports 文件夹查看所有导出文件。
                        </div>
                    `;
                    break;
                case 'status':
                    title.textContent = '📊 系统状态';
                    content.innerHTML = `
                        <h6>当前状态：</h6>
                        <ul>
                            <li>✅ Web服务器：运行中</li>
                            <li>✅ 配置模块：已加载</li>
                            <li>✅ 数据目录：已创建</li>
                            <li>✅ 导出功能：正常</li>
                        </ul>
                        <h6>访问信息：</h6>
                        <ul>
                            <li>地址：http://127.0.0.1:8000</li>
                            <li>端口：8000</li>
                            <li>状态：运行中</li>
                        </ul>
                    `;
                    break;
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
        """
        
        @app.route('/')
        def index():
            return render_template_string(INDEX_TEMPLATE)
        
        @app.route('/api/status')
        def status():
            return jsonify({
                "status": "running",
                "message": "股票数据管理平台运行正常",
                "modules": ["韭研数据", "开盘啦数据", "龙虎榜数据"]
            })
        
        return app
        
    except Exception as e:
        print(f"创建Web应用失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 启动股票数据管理平台...")
    print("=" * 50)
    
    # 初始化目录
    try:
        from config.settings import create_directories
        create_directories()
        print("✅ 项目目录初始化完成")
    except Exception as e:
        print(f"⚠️  目录初始化警告: {e}")
    
    # 创建Web应用
    app = create_minimal_web_app()
    if not app:
        print("❌ Web应用创建失败")
        return
    
    print("✅ Web应用创建成功")
    print("\n" + "=" * 50)
    print("🌐 平台访问信息:")
    print("   地址: http://127.0.0.1:8000")
    print("   状态: 运行中")
    print("\n📝 使用说明:")
    print("   1. 在浏览器中打开上述地址")
    print("   2. 点击不同的模块卡片")
    print("   3. 查看详细的使用说明")
    print("   4. 按照说明执行相应功能")
    print("\n💡 提示: 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    try:
        app.run(host='127.0.0.1', port=8000, debug=True)
    except KeyboardInterrupt:
        print("\n👋 平台已停止运行")
    except Exception as e:
        print(f"\n❌ 运行错误: {e}")

if __name__ == "__main__":
    main()
