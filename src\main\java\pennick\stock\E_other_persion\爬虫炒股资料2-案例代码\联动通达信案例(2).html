<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期查询</title>
</head>

<body>
    <input type="date" id="dateInput">
    <button id="queryButton">查询</button>
    <table id="dataTable" border="1">
        <thead>
            <tr>
                <th>股票中文名</th>
                <th>涨跌幅</th>
                <th>收盘价</th>
                <th>股票代码前六位</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>
    <script>
        const dateInput = document.getElementById('dateInput');
        const queryButton = document.getElementById('queryButton');
        const dataTable = document.getElementById('dataTable').getElementsByTagName('tbody')[0];

        queryButton.addEventListener('click', async () => {
            const selectedDate = dateInput.value;
            if (!selectedDate) {
                alert('请选择日期');
                return;
            }
            const apiUrl = `https://flash-api.xuangubao.com.cn/api/pool/detail?pool_name=limit_up&date=${selectedDate}`;
            try {
                const response = await fetch(apiUrl);
                const data = await response.json();
                if (data.code === 20000) {
                    dataTable.innerHTML = '';
                    data.data.forEach(item => {
                        const row = dataTable.insertRow();
                        const stockNameCell = row.insertCell(0);
                        const changePercentCell = row.insertCell(1);
                        const priceCell = row.insertCell(2);
                        const symbolCell = row.insertCell(3);

                        const symbolPrefix = item.symbol.slice(0, 6);
                        const link = document.createElement('a');
                        link.href = `http://www.treeid/code_${symbolPrefix}`;
                        link.textContent = item.stock_chi_name;
                        stockNameCell.appendChild(link);

                        changePercentCell.textContent = (item.change_percent * 100).toFixed(2) + '%';
                        priceCell.textContent = item.price;
                        symbolCell.textContent = symbolPrefix;
                    });
                } else {
                    alert('请求失败：' + data.message);
                }
            } catch (error) {
                alert('请求出错：' + error.message);
            }
        });
    </script>
</body>

</html>
    