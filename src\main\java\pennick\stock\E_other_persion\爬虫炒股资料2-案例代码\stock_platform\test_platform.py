#!/usr/bin/env python3
"""
股票数据平台测试脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from config.settings import DATA_SOURCES, create_directories
        print("✅ 配置模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_directory_creation():
    """测试目录创建"""
    print("\n🔍 测试目录创建...")
    
    try:
        from config.settings import create_directories
        create_directories()
        print("✅ 目录创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 目录创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 股票数据平台功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("目录创建", test_directory_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！平台可以正常使用")
        print("\n🚀 启动命令: python start.py")
        print("🌐 访问地址: http://127.0.0.1:8000")
    else:
        print("⚠️  部分测试失败，请检查相关模块")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
