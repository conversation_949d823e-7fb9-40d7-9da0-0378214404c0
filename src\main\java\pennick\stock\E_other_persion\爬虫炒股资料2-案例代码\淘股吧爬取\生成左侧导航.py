import os
from bs4 import BeautifulSoup
import re

def slugify(text):
    """将文本转换为适合URL的slug。"""
    text = re.sub(r'[^\w\u4e00-\u9fa5\s-]', '', text)  # 移除特殊字符，保留中文、英文、数字、空格和连字符
    text = re.sub(r'[-\s]+', '-', text).strip()      # 将空格和连字符替换为单个连字符
    return text

def process_html_file(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        html_content = f.read()

    soup = BeautifulSoup(html_content, 'html.parser')

    # 提取所有 <title> 标签的内容作为标题
    titles = [title.string for title in soup.find_all('title')]

    if not titles:
        print(f"在文件 {filepath} 中未找到标题。")
        return

    # 构建导航栏 HTML
    navbar_html = """
    <div id="navbar" style="position: fixed; top: 0; left: 0; height: 100%; width: 200px; background-color: #f8f8f8; border-right: 1px solid #ccc; padding-top: 20px; overflow-y: auto; user-select: none;">
        <div id="navbar-resizer" style="position: absolute; top: 0; right: 0; width: 5px; height: 100%; background-color: #ccc; cursor: col-resize;"></div>
        <ul style="list-style-type: none; padding: 0;">
    """

    # 查找所有的 <h1> 标签，并添加锚点和导航链接
    for i, title_text in enumerate(titles):
        slug = slugify(title_text)
        h1_tag = soup.find('h1', string=title_text) # 精确匹配 <h1> 标签内容
        if h1_tag:
            anchor_id = f"section-{slug}"
            anchor_tag = soup.new_tag("a", attrs={"id": anchor_id})
            h1_tag.insert_before(anchor_tag)
            navbar_html += f'<li><a href="#{anchor_id}">{title_text}</a></li>'
        else:
            print(f"警告: 在文件 {filepath} 中未找到与标题 '{title_text}' 对应的 <h1> 标签。")

    navbar_html += """
        </ul>
    </div>
    """

    # 添加 CSS 样式
    css_style = """
    <style>
        body {
            margin-left: 200px; /* 初始时为导航栏留出空间 */
            position: relative; /* 确保滚动监听正常工作 */
            transition: margin-left 0.3s ease; /* 添加过渡效果 */
        }
        #navbar .active {
            font-weight: bold;
            background-color: #e0e0e0;
        }
    </style>
    """

    # 添加 JavaScript 代码
    javascript_code = """
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.getElementById('navbar');
            const navbarResizer = document.getElementById('navbar-resizer');
            const body = document.body;
            const navLinks = document.querySelectorAll('#navbar a');
            const sections = {};
            let isResizing = false;
            let startX;
            let startWidth;

            navLinks.forEach(link => {
                const hash = link.getAttribute('href').substring(1);
                const target = document.getElementById(hash);
                if (target) {
                    sections[hash] = target.offsetTop;
                }
            });

            function highlightNavLink() {
                const scrollPosition = window.scrollY;
                let currentSection = '';

                for (const sectionId in sections) {
                    if (scrollPosition >= sections[sectionId] - 50) { // 减去一个偏移量，提前激活
                        currentSection = sectionId;
                    }
                }

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href').substring(1) === currentSection) {
                        link.classList.add('active');
                    }
                });
            }

            window.addEventListener('scroll', highlightNavLink);
            window.addEventListener('resize', function() {
                // 重新计算 sections 的 offsetTop，以应对窗口大小变化
                navLinks.forEach(link => {
                    const hash = link.getAttribute('href').substring(1);
                    const target = document.getElementById(hash);
                    if (target) {
                        sections[hash] = target.offsetTop;
                    }
                });
                highlightNavLink(); // 重新高亮
            });

            // 初始化高亮
            highlightNavLink();

            navbarResizer.addEventListener('mousedown', function(e) {
                isResizing = true;
                startX = e.clientX;
                startWidth = navbar.offsetWidth;
                document.addEventListener('mousemove', resizeNavbar);
                document.addEventListener('mouseup', stopResize);
            });

            function resizeNavbar(e) {
                if (!isResizing) return;
                const newWidth = startWidth + e.clientX - startX;
                navbar.style.width = newWidth + 'px';
                body.style.marginLeft = newWidth + 'px';
            }

            function stopResize() {
                isResizing = false;
                document.removeEventListener('mousemove', resizeNavbar);
                document.removeEventListener('mouseup', stopResize);
            }

            // ---  新增的代码  ---
            // 假设你的右侧文章内容在一个 id 为 "content" 的元素中
            const contentContainer = document.getElementById('content');
            if (contentContainer) {
                const observer = new MutationObserver(function(mutationsList, observer) {
                    for (let mutation of mutationsList) {
                        if (mutation.type === 'childList') {
                            // 当内容区域的子节点发生变化时，延迟执行高亮，确保新内容已渲染
                            setTimeout(highlightNavLink, 100);
                            break; // 假设一次内容切换只会替换子节点
                        }
                    }
                });

                const config = { childList: true, subtree: false };
                observer.observe(contentContainer, config);
            }
            // --- 新增代码结束 ---

        });
    </script>
    """

    # 将导航栏添加到 <body> 的开头
    body = soup.find('body')
    if body:
        body.insert(0, BeautifulSoup(navbar_html, 'html.parser'))
        # 将 CSS 添加到 <head>
        head = soup.find('head')
        if head:
            head.append(BeautifulSoup(css_style, 'html.parser'))
        else:
            body.append(BeautifulSoup(css_style, 'html.parser')) # 如果没有 head 标签，添加到 body

        # 将 JavaScript 添加到 <body> 的末尾
        body.append(BeautifulSoup(javascript_code, 'html.parser'))

        # 写回修改后的 HTML 内容
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        print(f"已处理文件: {filepath}")
    else:
        print(f"在文件 {filepath} 中未找到 <body> 标签。")

if __name__ == "__main__":
    for filename in os.listdir():
        if filename.endswith(".html"):
            process_html_file(filename)