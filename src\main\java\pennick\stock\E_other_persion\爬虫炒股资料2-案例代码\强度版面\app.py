from flask import Flask, jsonify, render_template, request
from flask_cors import CORS
import requests
from datetime import datetime
import time
try:
    from mootdx.quotes import Quotes
    import pandas as pd
    HAS_MOOTDX = True
except ImportError:
    print("警告: mootdx 库未安装，实时行情功能将不可用")
    print("请使用命令安装: pip install mootdx pandas")
    HAS_MOOTDX = False

app = Flask(__name__)
CORS(app)  # 启用CORS支持

def fetch_data_page(page=0):
    index = page * 60
    base_url = f'https://apphq.longhuvip.com/w1/api/index.php?Order=1&a=RealRankingInfo&st=60&apiv=w26&Type=1&c=ZhiShuRanking&PhoneOSNew=1&DeviceID=20ad85ca-becb-3bed-b3d4-30032a0f5923&Index={index}&ZSType=7'
    
    try:
        r = requests.get(base_url).json()['list']
        processed_data = []
        
        for item in r:
            if isinstance(item, dict):
                data = list(item.values())
            elif isinstance(item, list):
                data = item
                
            new_data = []
            needed_columns = [0, 1, 2, 6, 12, 4, 5, 9]
            
            for col in needed_columns:
                if len(data) > col:
                    value = data[col]
                    if col in [5, 6, 12]:
                        try:
                            value = round(float(value) / 100000000, 2)
                        except (ValueError, IndexError):
                            pass
                    new_data.append(value)
                else:
                    new_data.append(None)
                    
            processed_data.append(new_data)
            
        return processed_data
    except Exception as e:
        print(f"Error fetching data for page {page}: {str(e)}")
        return []

def fetch_stock_data(plate_id, end_time):
    """获取特定板块的股票数据"""
    url_base = f'https://apphq.longhuvip.com/w1/api/index.php?Order=1&TSZB=0&a=ZhiShuStockList_W8&st=1000&c=ZhiShuRanking&PhoneOSNew=1&RStart=0925&old=1&DeviceID=d66474b3-fd78-3a95-a56d-76e29e765ea3&VerSion=********&IsZZ=0&Token=395184c6542d28c3e01b3707f59d1db8&apiv=w39&Type=1&IsKZZType=0&UserID=1214525&PlateID={plate_id}&TSZB_Type=0&filterType=0&REnd={end_time}'
    
    # 打印完整URL到控制台
    print("\n=== 调试信息 ===")
    print(f"板块ID: {plate_id}")
    print(f"结束时间: {end_time}")
    print(f"完整URL: {url_base}")
    print("=== 调试信息结束 ===\n")
    
    all_data = []
    max_pages = 10  # 限制页数，避免请求过多
    
    for page in range(max_pages):
        index = page * 1000
        url = url_base + f'&Index={index}'
        
        # 打印分页URL
        print(f"请求第 {page + 1} 页，URL: {url}")
        
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()
            
            if 'list' in data and data['list']:
                all_data.extend(data['list'])
            else:
                print(f"第 {page + 1} 页返回数据中没有'list'字段或为空")
                break
                
            # 如果返回的数据少于1000条，说明已经到达最后一页
            if len(data.get('list', [])) < 1000:
                break
                
            # 避免请求过快
            if page < max_pages - 1:
                time.sleep(0.2)
                
        except requests.RequestException as e:
            print(f"请求第 {page + 1} 页出错: {e}")
            break
        except ValueError as e:
            print(f"解析第 {page + 1} 页 JSON 出错: {e}")
            break
    
    return all_data

def get_realtime_stock_quotes(stock_codes):
    """获取股票实时行情数据并计算涨幅"""
    if not HAS_MOOTDX:
        return None
    
    try:
        # 确保股票代码格式正确
        formatted_codes = []
        for code in stock_codes:
            # 移除可能的前缀（如sh.、sz.）
            clean_code = code.replace('sh.', '').replace('sz.', '')
            formatted_codes.append(clean_code)
        
        # 使用mootdx获取实时行情
        client = Quotes.factory(market='std')
        quotes_data = client.quotes(symbol=formatted_codes)
        
        # 计算涨幅
        quotes_data['increase_rate'] = ((quotes_data['price'] - quotes_data['last_close']) / quotes_data['last_close'] * 100).round(2)
        
        # 只选取 code 和 increase_rate 列
        result = quotes_data[['code', 'increase_rate']]
        
        # 转换为字典列表
        result_list = []
        for index, row in result.iterrows():
            result_list.append({
                'code': row['code'],
                'increase_rate': row['increase_rate']
            })
        
        return result_list
    except Exception as e:
        print(f"获取实时行情出错: {str(e)}")
        return None

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    # 获取第一页数据
    page = request.args.get('page', default=0, type=int)
    data = fetch_data_page(page)
    
    # 返回单页数据
    return jsonify({
        'data': data,
        'page': page,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'has_more': len(data) > 0  # 如果有数据，表示可能有下一页
    })

@app.route('/api/stocks')
def get_stocks():
    # 获取板块ID和结束时间
    plate_id = request.args.get('plateId', default='', type=str)
    end_time = request.args.get('endTime', default='', type=str)
    
    if not plate_id or not end_time:
        return jsonify({
            'success': False,
            'message': 'Missing required parameters: plateId or endTime'
        })
    
    # 获取股票数据
    stock_data = fetch_stock_data(plate_id, end_time)
    
    # 返回数据
    return jsonify({
        'success': True,
        'data': stock_data,
        'count': len(stock_data),
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

@app.route('/api/realtime_quotes')
def get_realtime_quotes():
    # 获取股票代码列表
    stock_codes = request.args.get('codes', default='', type=str)
    
    if not stock_codes:
        return jsonify({
            'success': False,
            'message': 'Missing required parameter: codes'
        })
    
    # 将股票代码字符串分割为列表
    code_list = stock_codes.split(',')
    
    # 获取实时行情数据
    quotes_data = get_realtime_stock_quotes(code_list)
    
    if quotes_data is None:
        return jsonify({
            'success': False,
            'message': 'Failed to get realtime quotes or mootdx library not installed'
        })
    
    # 返回数据
    return jsonify({
        'success': True,
        'data': quotes_data,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

if __name__ == '__main__':
    app.run(debug=True, port=5000) 