#!/usr/bin/env python3
"""
股票数据平台完整启动脚本
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖"""
    required_packages = ['flask', 'requests', 'pandas']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install flask requests pandas openpyxl")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def test_crawler():
    """测试爬虫功能"""
    print("\n🔍 测试爬虫功能...")
    try:
        from crawlers.kaipanla_crawler import KaipanlaCrawler
        
        crawler = KaipanlaCrawler()
        print(f"✅ 开盘啦爬虫初始化成功")
        print(f"   支持的数据类型: {crawler.config['data_types']}")
        
        # 简单测试（不实际请求）
        print("✅ 爬虫功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 爬虫功能测试失败: {e}")
        return False

def start_web_app():
    """启动Web应用"""
    try:
        from web.app import app, init_app
        from config.settings import WEB_CONFIG
        
        print("🚀 正在启动股票数据管理平台...")
        print(f"📍 访问地址: http://{WEB_CONFIG['host']}:{WEB_CONFIG['port']}")
        print("\n📝 功能说明:")
        print("   ✅ 开盘啦数据: 涨停、跌停、竞价、炸板")
        print("   ✅ 自动数据处理和Excel导出")
        print("   ✅ Web界面管理和文件下载")
        print("   ✅ 实时执行日志显示")
        print("\n🎯 使用方法:")
        print("   1. 点击'开盘啦数据'模块")
        print("   2. 选择日期和数据类型")
        print("   3. 点击执行获取数据")
        print("   4. 在右侧下载Excel文件")
        print("\n" + "="*50)
        
        # 初始化应用
        init_app()
        
        # 启动Flask应用
        app.run(
            host=WEB_CONFIG["host"],
            port=WEB_CONFIG["port"],
            debug=WEB_CONFIG["debug"]
        )
        
    except Exception as e:
        print(f"❌ 启动Web应用失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 股票数据管理平台完整版启动")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 初始化目录
    try:
        from config.settings import create_directories
        create_directories()
        print("✅ 项目目录初始化完成")
    except Exception as e:
        print(f"⚠️  目录初始化警告: {e}")
    
    # 测试爬虫功能
    if not test_crawler():
        print("⚠️  爬虫功能测试失败，但仍可启动Web界面")
    
    print("\n✅ 所有检查完成，准备启动...")
    print("="*50)
    
    # 启动Web应用
    start_web_app()

if __name__ == "__main__":
    main()
