import requests
from openpyxl import Workbook
import datetime

url_base = 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=5&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day=2025-02-21&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&'

# 初始化变量以存储所有数据
all_data = []

# 初始化索引值
index = 0

# 循环发送请求，直到获取所有数据为止
while True:
    url = url_base + f'&Index={index}'
    response = requests.get(url).json()
    if 'list' in response:
        data = response['list']
        if len(data) == 0:
            # 如果没有更多数据，退出循环
            break
        else:
            all_data.extend(data)
            # 更新索引值以获取下一页数据
            index += 60
    else:
        print("JSON 数据中不存在 'list' 键")
        break

# 创建一个新的工作簿
wb = Workbook()
# 获取活动工作表
ws = wb.active

# 如果有数据，生成默认表头
if all_data:
    num_columns = len(all_data[0])
    headers = [f'Column {i+1}' for i in range(num_columns)]
    ws.append(headers)

    # 逐行写入数据
    for item in all_data:
        ws.append(item)

    # 要删除的列名
    columns_to_delete = [
        'Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 9', 'Column 10',
        'Column 11', 'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
        'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
        'Column 28', 'Column 29', 'Column 30', 'Column 31', 'Column 32', 'Column 33'
    ]

    # 获取表头行
    header_row = ws[1]
    # 用于存储要删除的列索引
    columns_to_delete_index = []
    column7_index = None  # 用于存储 "Column 7" 的索引
    column8_index = None  # 用于存储 "Column 8" 的索引  新增
    column9_index = None  # 用于存储 "Column 9" 的索引
    column13_index = None # 用于存储 "Column 13" 的索引
    column14_index = None # 用于存储 "Column 14" 的索引
    column15_index = None # 用于存储 "Column 15" 的索引
    column16_index = None # 用于存储 "Column 16" 的索引

    # 遍历表头，找到要删除的列的索引 和 需要处理的列的索引
    for col_idx, cell in enumerate(header_row, start=1):
        if cell.value in columns_to_delete:
            columns_to_delete_index.append(col_idx)
        if cell.value == 'Column 7':
            column7_index = col_idx
        if cell.value == 'Column 8':
            column8_index = col_idx  # 获取 "Column 8" 的索引
        if cell.value == 'Column 9':
            column9_index = col_idx
        if cell.value == 'Column 13':
            column13_index = col_idx
        if cell.value == 'Column 14':
            column14_index = col_idx
        if cell.value == 'Column 15':
            column15_index = col_idx
        if cell.value == 'Column 16':
            column16_index = col_idx

    # 转换 "Column 7" 的时间戳为 时分 格式
    if column7_index is not None:
        for row_idx in range(2, ws.max_row + 1):  # 从第二行开始，跳过表头
            cell = ws.cell(row=row_idx, column=column7_index)
            timestamp = cell.value
            if isinstance(timestamp, (int, float, str)): # 确保是时间戳类型
                try:
                    timestamp_int = int(timestamp) # 转换为整数
                    if len(str(timestamp_int)) == 10: # 假设是10位时间戳，秒级
                        datetime_obj = datetime.datetime.fromtimestamp(timestamp_int)
                        cell.value = datetime_obj.strftime('%H:%M') # 格式化为 时:分
                    elif len(str(timestamp_int)) == 13: # 假设是13位时间戳，毫秒级
                        datetime_obj = datetime.datetime.fromtimestamp(timestamp_int / 1000)
                        cell.value = datetime_obj.strftime('%H:%M') # 格式化为 时:分
                except ValueError:
                    print(f"第 {row_idx} 行 Column 7 的值 '{timestamp}' 无法转换为时间戳。")

    # 转换 "Column 8" 的时间戳为 时分 格式   新增
    if column8_index is not None:
        for row_idx in range(2, ws.max_row + 1):  # 从第二行开始，跳过表头
            cell = ws.cell(row=row_idx, column=column8_index)
            timestamp = cell.value
            if isinstance(timestamp, (int, float, str)): # 确保是时间戳类型
                try:
                    timestamp_int = int(timestamp) # 转换为整数
                    if len(str(timestamp_int)) == 10: # 假设是10位时间戳，秒级
                        datetime_obj = datetime.datetime.fromtimestamp(timestamp_int)
                        cell.value = datetime_obj.strftime('%H:%M') # 格式化为 时:分
                    elif len(str(timestamp_int)) == 13: # 假设是13位时间戳，毫秒级
                        datetime_obj = datetime.datetime.fromtimestamp(timestamp_int / 1000)
                        cell.value = datetime_obj.strftime('%H:%M') # 格式化为 时:分
                except ValueError:
                    print(f"第 {row_idx} 行 Column 8 的值 '{timestamp}' 无法转换为时间戳。")


    # 处理 "Column 9"
    if column9_index is not None:
        for row_idx in range(2, ws.max_row + 1):
            cell = ws.cell(row=row_idx, column=column9_index)
            value = cell.value
            if isinstance(value, (int, float, str)):
                try:
                    value_float = float(value)
                    cell.value = "{:.2f}".format(value_float / 10000)
                except ValueError:
                    print(f"第 {row_idx} 行 Column 9 的值 '{value}' 无法转换为数字。")

    # 处理 "Column 13"
    if column13_index is not None:
        for row_idx in range(2, ws.max_row + 1):
            cell = ws.cell(row=row_idx, column=column13_index)
            value = cell.value
            if isinstance(value, (int, float, str)):
                try:
                    value_float = float(value)
                    cell.value = "{:.2f}".format(value_float / 10000)
                except ValueError:
                    print(f"第 {row_idx} 行 Column 13 的值 '{value}' 无法转换为数字。")

    # 处理 "Column 14"
    if column14_index is not None:
        for row_idx in range(2, ws.max_row + 1):
            cell = ws.cell(row=row_idx, column=column14_index)
            value = cell.value
            if isinstance(value, (int, float, str)):
                try:
                    value_float = float(value)
                    cell.value = "{:.2f}".format(value_float / 100000000)
                except ValueError:
                    print(f"第 {row_idx} 行 Column 14 的值 '{value}' 无法转换为数字。")

    # 处理 "Column 16"
    if column16_index is not None:
        for row_idx in range(2, ws.max_row + 1):
            cell = ws.cell(row=row_idx, column=column16_index)
            value = cell.value
            if isinstance(value, (int, float, str)):
                try:
                    value_float = float(value)
                    cell.value = "{:.2f}".format(value_float / 100000000)
                except ValueError:
                    print(f"第 {row_idx} 行 Column 16 的值 '{value}' 无法转换为数字。")

    # 替换表头名称
    if column7_index is not None:
        ws.cell(row=1, column=column7_index).value = '跌停时间'
    if column8_index is not None:
        ws.cell(row=1, column=column8_index).value = '开板时间' #  新增 Column 8 表头替换
    if column9_index is not None:
        ws.cell(row=1, column=column9_index).value = '开板时间'
    if column13_index is not None:
        ws.cell(row=1, column=column13_index).value = '主力净额'
    if column14_index is not None:
        ws.cell(row=1, column=column14_index).value = '成交金额'
    if column15_index is not None:
        ws.cell(row=1, column=column15_index).value = '换手率'
    if column16_index is not None:
        ws.cell(row=1, column=column16_index).value = '实际流通'


    # 按降序排列要删除的列索引，避免删除列时索引混乱
    columns_to_delete_index.sort(reverse=True)

    # 删除指定的列
    for col_idx in columns_to_delete_index:
        ws.delete_cols(col_idx)
else:
    print("all_data 列表为空，没有数据可写入。")

# 保存工作簿到文件
wb.save('output.xlsx')
print("数据已成功保存到 output.xlsx 文件中。")