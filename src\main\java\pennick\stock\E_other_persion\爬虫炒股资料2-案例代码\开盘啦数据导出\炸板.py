import requests
from openpyxl import Workbook
from datetime import datetime

url_base = 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=2&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=4&Filter=0&Day=2025-02-21&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&'

# 初始化变量以存储所有数据
all_data = []

# 初始化索引值
index = 0

# 循环发送请求，直到获取所有数据为止
while True:
    url = url_base + f'&Index={index}'
    response = requests.get(url).json()
    if 'list' in response:
        data = response['list']
        if len(data) == 0:
            # 如果没有更多数据，退出循环
            break
        else:
            all_data.extend(data)
            # 更新索引值以获取下一页数据
            index += 60
    else:
        print("JSON 数据中不存在 'list' 键")
        break

# 创建一个新的工作簿
wb = Workbook()
# 获取活动工作表
ws = wb.active

# 如果有数据，生成默认表头
if all_data:
    num_columns = len(all_data[0])
    headers = [f'Column {i + 1}' for i in range(num_columns)]

    # 替换指定列名称
    column_mapping = {
        'Column 13': '主力净额',
        'Column 14': '成交额',
        'Column 15': '实际换手',
        'Column 7': '涨停时间',
        'Column 7': '开板时间',
        'Column 16': '实际流通'
    }
    for i, header in enumerate(headers):
        if header in column_mapping:
            headers[i] = column_mapping[header]

    ws.append(headers)

    # 逐行写入数据
    for item in all_data:
        # 转换 Column 7 和 Column 8 的时间戳为时分格式
        try:
            # 索引从 0 开始，Column 7 对应索引 6，Column 8 对应索引 7
            timestamp_7 = int(item[6])
            timestamp_8 = int(item[7])
            time_7 = datetime.fromtimestamp(timestamp_7).strftime("%H:%M")
            time_8 = datetime.fromtimestamp(timestamp_8).strftime("%H:%M")
            item[6] = time_7
            item[7] = time_8
        except (IndexError, ValueError):
            pass

        # 处理 Column 13、Column 14 和 Column 16 的值
        try:
            # Column 13 对应索引 12，Column 14 对应索引 13，Column 16 对应索引 15
            item[12] = round(float(item[12]) / 100000000, 2)
            item[13] = round(float(item[13]) / 100000000, 2)
            item[15] = round(float(item[15]) / 100000000, 2)
        except (IndexError, ValueError):
            pass

        ws.append(item)
else:
    print("all_data 列表为空，没有数据可写入。")

# 要删除的列名
columns_to_delete = [
    'Column 3', 'Column 4', 'Column 6', 'Column 9', 'Column 11',
    'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
    'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26',
    'Column 27', 'Column 28', 'Column 29', 'Column 31', 'Column 32','Column 30',
    'Column 33'
]

# 获取表头行
header_row = next(ws.iter_rows(min_row=1, max_row=1, values_only=True))

# 存储要删除的列索引
columns_to_delete_index = []
for col_index, header in enumerate(header_row, start=1):
    if header in columns_to_delete:
        columns_to_delete_index.append(col_index)

# 按降序排列要删除的列索引，避免删除列时索引错乱
columns_to_delete_index.sort(reverse=True)

# 删除指定列
for col_index in columns_to_delete_index:
    ws.delete_cols(col_index)

# 保存工作簿到文件
wb.save('output.xlsx')
print("数据已成功保存到 output.xlsx 文件中。")