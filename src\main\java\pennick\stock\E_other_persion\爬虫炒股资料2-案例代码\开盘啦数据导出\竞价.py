import requests
from openpyxl import Workbook
from openpyxl.utils import get_column_letter

url_base = 'https://apphis.longhuvip.com/w1/api/index.php?Filter=0&FilterGem=0&FilterMotherboard=0&FilterTIB=0&Index=0&Is_st=1&Order=1&PhoneOSNew=2&PidType=8&Type=18&VerSion=*******&a=HisDaBanList&apiv=w32&c=HisHomeDingPan&st=60&Day=2025-02-21'

# 初始化变量以存储所有数据
all_data = []

# 初始化索引值
index = 0

# 循环发送请求，直到获取所有数据为止
while True:
    url = url_base + f'&Index={index}'
    response = requests.get(url).json()
    if 'list' in response:
        data = response['list']
        if len(data) == 0:
            # 如果没有更多数据，退出循环
            break
        else:
            all_data.extend(data)
            # 更新索引值以获取下一页数据
            index += 60
    else:
        print("JSON 数据中不存在 'list' 键")
        break

# 创建一个新的工作簿
wb = Workbook()
# 获取活动工作表
ws = wb.active

# 如果有数据，生成默认表头
if all_data:
    num_columns = len(all_data[0])
    headers = [f'Column {i + 1}' for i in range(num_columns)]
    ws.append(headers)

    # 逐行写入数据
    for item in all_data:
        ws.append(item)
else:
    print("all_data 列表为空，没有数据可写入。")

# 要删除的列的表头
columns_to_delete = [
    'Column 3', 'Column 4', 'Column 6', 'Column 7', 'Column 8', 'Column 9',
    'Column 10', 'Column 11', 'Column 13', 'Column 14', 'Column 15','Column 30','Column 17', 'Column 18',
    'Column 24', 'Column 25', 'Column 26', 'Column 27', 'Column 28', 'Column 29',
    'Column 31', 'Column 32', 'Column 33'
]

# 获取表头行
header_row = next(ws.iter_rows(min_row=1, max_row=1, values_only=True))

# 存储要删除的列的索引
columns_to_delete_index = []
for col_index, header in enumerate(header_row, start=1):
    if header in columns_to_delete:
        columns_to_delete_index.append(col_index)

# 按降序排列要删除的列的索引，避免删除列时索引混乱
columns_to_delete_index.sort(reverse=True)

# 删除指定的列
for col_index in columns_to_delete_index:
    ws.delete_cols(col_index)

# 要处理的列及其对应的除数
columns_to_process = {
    'Column 19': 100000000,
    'Column 21': 10000,
'Column 20': 1,
    'Column 23': 10000,
    'Column 16': 100000000
}

# 重新获取表头行，因为之前可能删除了一些列
header_row = next(ws.iter_rows(min_row=1, max_row=1, values_only=True))

# 遍历要处理的列
for col_name, divisor in columns_to_process.items():
    if col_name in header_row:
        col_index = header_row.index(col_name) + 1
        col_letter = get_column_letter(col_index)
        # 从第二行开始处理数据行
        for row in range(2, ws.max_row + 1):
            cell = ws[f'{col_letter}{row}']
            try:
                value = float(cell.value)
                new_value = round(value / divisor, 2)
                cell.value = new_value
            except (ValueError, TypeError):
                continue

# 表头替换字典
header_replacements = {
    "Column 19": "涨停委买额",
    "Column 5": "实时涨幅",
    "Column 16": "实际流通",
    "Column 20": "竞价涨幅",
    "Column 21": "竞价净额",
"Column 22": "竞价换手",
    "Column 23": "竞价成交额"
}

# 重新获取表头行
header_row = next(ws.iter_rows(min_row=1, max_row=1, values_only=True))
header_row_cells = next(ws.iter_rows(min_row=1, max_row=1))

for i, header in enumerate(header_row):
    if header in header_replacements:
        header_row_cells[i].value = header_replacements[header]

# 保存工作簿到文件
wb.save('output.xlsx')
print("数据已成功保存到 output.xlsx 文件中。")