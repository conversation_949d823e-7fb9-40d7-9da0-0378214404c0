<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据展示</title>
    <!-- 添加xlsx库 -->
    <script src="https://cdn.sheetjs.com/xlsx-0.19.3/package/dist/xlsx.full.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 5px;
            font-family: Arial, sans-serif;
            background-color: #000;
            color: #eee;
        }
        .container {
            display: flex;
            height: calc(100vh - 40px);
            position: relative;
        }
        .left-panel, .right-panel {
            border: 1px solid #333;
            border-radius: 5px;
            padding: 5px;
            overflow: hidden;
            background-color: #111;
            display: flex;
            flex-direction: column;
        }
        .left-panel {
            width: 35%;
            margin-right: 10px;
        }
        .right-panel {
            flex: 1;
            margin-left: 10px;
        }
        .resizer {
            width: 5px;
            height: 100%;
            background-color: #333;
            position: absolute;
            left: auto;
            right: calc(100% - 35% - 20px);
            transform: none;
            cursor: col-resize;
            z-index: 10;
        }
        .resizer:hover, .resizer.active {
            background-color: #4CAF50;
        }
        .panel-header {
            margin-bottom: 10px;
        }
        .table-container {
            overflow: auto;
            flex: 1;
            width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 100%;
        }
        thead {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #111;
        }
        th, td {
            border: 1px solid #333;
            padding: 8px;
            text-align: left;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        th {
            background-color: #222;
            cursor: pointer;
            user-select: none;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 2;
            position: relative;
        }
        th:hover {
            background-color: #2a2a2a;
        }
        th.dragging {
            opacity: 0.5;
            background-color: #333;
        }
        th.drag-over {
            border-left: 2px solid #ff5555;
        }
        .timestamp {
            color: #999;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .status {
            color: #999;
            font-size: 14px;
            margin-top: 10px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.1);
            border-radius: 50%;
            border-top-color: #4CAF50;
            animation: spin 1s ease-in-out infinite;
            vertical-align: middle;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        h2 {
            color: #ff5555;
            margin-top: 0;
        }
        .clickable {
            cursor: pointer;
            color: #ff5555;
            text-decoration: none;
        }
        .clickable:hover {
            color: #ff8888;
        }
        #rightTable td.positive {
            color: #ff5555;
        }
        #rightTable td.negative {
            color: #55ff55;
        }
        
        /* 自定义滚动条样式 */
        .table-container::-webkit-scrollbar {
            width: 5px;
            height: 5px;
        }
        
        .table-container::-webkit-scrollbar-track {
            background: #222;
            border-radius: 3px;
        }
        
        .table-container::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 3px;
        }
        
        .table-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 添加导出按钮样式 */
        .export-btn {
            background-color: #ff5555;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 13px;
        }
        .export-btn:hover {
            background-color: #ff6666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="left-panel">
            <div class="panel-header">
                <h2>开盘啦 - 板块强度（实时）</h2>
                <div class="timestamp" id="timestamp"></div>
            </div>
            <div class="table-container">
                <table id="dataTable">
                    <thead>
                        <tr>
                            <th data-index="0" style="width: 53px;" title="板块代码">板块代码</th>
                            <th data-index="1" style="width: 65px;" title="板块">板块</th>
                            <th data-index="2" style="width: 34px;" title="强度">强度</th>
                            <th data-index="3" style="width: 50px;" title="主力净额">主力净额</th>
                            <th data-index="4" style="width: 40px;" title="300W大单净额">300W大单净额</th>
                            <th data-index="5" style="width: 40px;" title="涨速">涨速</th>
                            <th data-index="6" style="width: 40px;" title="成交额">成交额</th>
                            <th data-index="7" style="width: 40px;" title="量比">量比</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody"></tbody>
                </table>
            </div>
            <div class="status" id="status"></div>
        </div>
        <div class="resizer" id="resizer"></div>
        <div class="right-panel">
            <div class="panel-header">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                    <h2 id="rightPanelTitle">成分股（请点击板块）</h2>
                    <div style="display: flex; align-items: center;">
                        <div class="refresh-settings" style="display: flex; align-items: center; gap: 5px;">
                            <span>刷新间隔:</span>
                            <input type="number" id="refreshInterval" min="3" value="10" style="width:50px; background-color: #222; color: #eee; border: 1px solid #444; padding: 3px;">
                            <span>秒</span>
                            <button id="setRefreshBtn" style="background-color: #333; color: #eee; border: 1px solid #444; padding: 3px 8px; cursor: pointer;">确定</button>
                        </div>
                        <button id="exportBtn" class="export-btn">导出</button>
                    </div>
                </div>
                <div id="rightPanelStatus" class="status"></div>
            </div>
            <div class="table-container">
                <table id="rightTable">
                    <thead>
                        <tr>
                            <th style="width: 53px;" title="代码">代码</th>
                            <th style="width: 60px;" title="股票名称">股票名称</th>
                            <th style="width: 130px;" title="板块">板块</th>
                            <th style="width: 40px;" title="涨幅">涨幅</th>
                            <th style="width: 63px;" title="主力净额">主力<br>净额</th>
                            <th style="width: 60px;" title="几板">几板</th>
                            <th style="width: 40px;" title="实际流通">实际<br>流通</th>
                            <th style="width: 40px;" title="价格">价格</th>
                            <th style="width: 40px;" title="领涨次数">领涨<br>次数</th>
                            <th style="width: 40px;" title="量比">量比</th>
                            <th style="width: 40px;" title="成交额">成交<br>额</th>
                            <th style="width: 40px;" title="实际换手">实际<br>换手</th>
                        </tr>
                    </thead>
                    <tbody id="rightTableBody"></tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentData = [];
        let currentPage = 0;
        let isLoading = false;
        let hasMorePages = true;
        let autoRefreshTimer = null;
        
        // 定义右侧表格列的映射关系（全局常量）
        const RIGHT_TABLE_COLUMNS = [
            {index: 0, name: "代码"},
            {index: 1, name: "股票名称"},
            {index: 4, name: "板块"},
            {index: 6, name: "涨幅", isPercent: true},
            {index: 13, name: "主力净额"},
            {index: 23, name: "几天几板"},
            {index: 10, name: "实际流通"},
            {index: 5, name: "价格"},
            {index: 40, name: "领涨次数"},
            {index: 21, name: "量比"},
            {index: 7, name: "成交额"},
            {index: 8, name: "实际换手", isPercent: true}
        ];
        
        // 用于跟踪右侧表格的排序状态
        let rightTableSortState = {
            column: 3, // 默认是涨幅列
            direction: 'desc' // 默认是从大到小排序
        };
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 加载保存的刷新间隔设置
            loadRefreshIntervalSetting();
            
            // 初始化表格列拖拽功能
            initTableColumnDrag();
            
            // 绑定左侧表头排序事件
            const leftHeaders = document.querySelectorAll('#dataTable th');
            leftHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));
                    sortTable(index);
                });
            });
            
            // 绑定右侧表头排序事件
            const rightHeaders = document.querySelectorAll('#rightTable th');
            rightHeaders.forEach((header, index) => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    sortRightTable(index);
                });
            });
            
            // 绑定刷新间隔设置按钮事件
            document.getElementById('setRefreshBtn').addEventListener('click', function() {
                setRefreshInterval();
            });
            
            // 允许按回车键设置刷新间隔
            document.getElementById('refreshInterval').addEventListener('keyup', function(event) {
                if (event.key === 'Enter') {
                    setRefreshInterval();
                }
            });
            
            // 绑定导出按钮事件
            document.getElementById('exportBtn').addEventListener('click', exportToExcel);
            
            // 首次加载数据
            startDataLoading();
            
            // 设置定时刷新
            autoRefreshTimer = setInterval(refreshData, 60000);
            
            // 初始化分割线拖动
            initResizer();
        });
        
        // 初始化分割线拖动功能
        function initResizer() {
            const resizer = document.getElementById('resizer');
            const container = document.querySelector('.container');
            const leftPanel = document.querySelector('.left-panel');
            const rightPanel = document.querySelector('.right-panel');
            let isResizing = false;
            
            // 鼠标按下事件
            resizer.addEventListener('mousedown', function(e) {
                isResizing = true;
                resizer.classList.add('active');
                document.body.style.cursor = 'col-resize';
                document.body.style.userSelect = 'none';
                e.preventDefault();
            });
            
            // 鼠标移动事件
            document.addEventListener('mousemove', function(e) {
                if (!isResizing) return;
                
                const containerRect = container.getBoundingClientRect();
                let leftWidth = e.clientX - containerRect.left;
                
                // 限制最小宽度
                const minWidth = 200;
                const maxWidth = containerRect.width - minWidth;
                
                if (leftWidth < minWidth) leftWidth = minWidth;
                if (leftWidth > maxWidth) leftWidth = maxWidth;
                
                // 计算百分比
                const leftPercent = (leftWidth / containerRect.width) * 100;
                
                // 更新面板宽度
                leftPanel.style.width = `${leftPercent}%`;
                
                // 更新拖动手柄位置 - 使其始终贴在左侧面板右侧
                resizer.style.right = `calc(100% - ${leftPercent}% - 10px)`;
                
                // 更新右侧面板的margin
                rightPanel.style.flex = '1';
            });
            
            // 鼠标松开事件
            document.addEventListener('mouseup', function() {
                if (isResizing) {
                    isResizing = false;
                    resizer.classList.remove('active');
                    document.body.style.cursor = '';
                    document.body.style.userSelect = '';
                }
            });
        }
        
        // 开始加载数据流程
        function startDataLoading() {
            // 重置状态
            currentData = [];
            currentPage = 0;
            hasMorePages = true;
            
            // 清空表格
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';
            
            // 设置状态消息
            updateStatus('加载中...', true);
            
            // 加载第一页
            fetchNextPage();
        }
        
        // 刷新数据
        function refreshData() {
            // 停止之前的所有请求
            isLoading = false;
            
            // 重新开始加载
            startDataLoading();
        }
        
        // 加载下一页数据
        function fetchNextPage() {
            if (isLoading || !hasMorePages) return;
            
            isLoading = true;
            updateStatus(`正在加载第 ${currentPage + 1} 页数据...`, true);
            
            fetch(`http://localhost:5000/api/data?page=${currentPage}`)
                .then(response => response.json())
                .then(data => {
                    // 合并数据
                    if (data.data && data.data.length > 0) {
                        // 添加新数据到表格
                        appendTableData(data.data);
                        
                        // 合并到全局数据
                        currentData = [...currentData, ...data.data];
                        
                        // 更新时间戳
                        updateTimestamp(data.timestamp);
                        
                        // 检查是否还有更多页
                        hasMorePages = data.has_more;
                        
                        // 更新页码
                        currentPage++;
                        
                        // 显示状态
                        if (hasMorePages) {
                            updateStatus(`已加载 ${currentData.length} 条数据，正在加载更多...`, true);
                            
                            // 延迟加载下一页，避免请求过快
                            setTimeout(() => {
                                isLoading = false;
                                fetchNextPage();
                            }, 500);
                        } else {
                            updateStatus(`共加载 ${currentData.length} 条数据`, false);
                            isLoading = false;
                        }
                    } else {
                        hasMorePages = false;
                        updateStatus(`加载完成，共 ${currentData.length} 条数据`, false);
                        isLoading = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    updateStatus('加载出错，请刷新页面重试', false);
                    isLoading = false;
                });
        }
        
        // 将数据追加到表格
        function appendTableData(data) {
            const tbody = document.getElementById('tableBody');
            
            data.forEach(row => {
                const tr = document.createElement('tr');
                
                row.forEach((cell, index) => {
                    const td = document.createElement('td');
                    let value = cell !== null ? cell : '';
                    
                    // 处理量比列（索引为7）
                    if (index === 7 && value !== '') {
                        value = parseFloat(value).toFixed(1);
                    }
                    
                    td.textContent = value;
                    
                    // 将板块名称（索引为1）设为可点击
                    if (index === 1) {
                        td.classList.add('clickable');
                        td.style.width = '65px';
                        td.style.whiteSpace = 'nowrap';
                        td.style.overflow = 'hidden';
                        td.style.textOverflow = 'ellipsis';
                        td.title = cell; // 添加title属性，使鼠标悬浮时显示完整内容
                        td.addEventListener('click', () => {
                            const plateCode = row[0]; // 获取板块代码（索引为0）
                            const plateName = cell;   // 板块名称
                            fetchStockData(plateCode, plateName);
                        });
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }

        // 获取股票数据
        function fetchStockData(plateId, plateName) {
            // 保存当前板块信息到全局变量，用于后续刷新
            window.currentPlateId = plateId;
            window.currentPlateName = plateName;
            
            // 更新右侧面板标题
            document.getElementById('rightPanelTitle').textContent = `${plateName}  - 成分股`;
            
            // 更新状态
            updateRightPanelStatus('加载中...', true);
            
            // 清空右侧表格
            document.getElementById('rightTableBody').innerHTML = '';
            
            // 获取股票数据并显示
            fetchAndDisplayStockData(plateId, plateName);
            
            // 清除之前的定时器
            if (window.stockRefreshTimer) {
                clearInterval(window.stockRefreshTimer);
            }
            
            if (window.realtimeQuotesTimer) {
                clearInterval(window.realtimeQuotesTimer);
            }
            
            // 设置新的定时器，检查是否需要刷新数据
            window.stockRefreshTimer = setInterval(checkAndRefreshStockData, 1000);
        }
        
        // 检查是否需要刷新股票数据
        function checkAndRefreshStockData() {
            // 如果没有当前板块信息，则不刷新
            if (!window.currentPlateId || !window.currentPlateName) return;
            
            const now = new Date();
            const seconds = now.getSeconds();
            const minutes = now.getMinutes();
            
            // 检查是否刚好过了整点或5分钟，且秒数为3秒
            if ((minutes % 5 === 0) && seconds === 3) {
                console.log(`时间: ${now.getHours()}:${minutes}:${seconds}, 执行自动刷新`);
                fetchAndDisplayStockData(window.currentPlateId, window.currentPlateName, true);
            }
        }
        
        // 获取并显示股票数据
        function fetchAndDisplayStockData(plateId, plateName, isAutoRefresh = false) {
            // 获取当前时间
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes();
            
            // 时间检查和调整
            let timeString;
            
            // 将当前时间转换为分钟数，便于比较
            const currentTimeInMinutes = hours * 60 + minutes;
            
            // 检查是否在交易时间内
            const isMorningSession = (currentTimeInMinutes >= 9 * 60 + 3) && (currentTimeInMinutes <= 11 * 60 + 30); // 9:03-11:30
            const isAfternoonSession = (currentTimeInMinutes >= 13 * 60) && (currentTimeInMinutes <= 15 * 60); // 13:00-15:00
            const isLunchBreak = (currentTimeInMinutes > 11 * 60 + 30) && (currentTimeInMinutes < 13 * 60); // 11:30-13:00
            
            if (isMorningSession || isAfternoonSession) {
                // 在交易时间内，使用当前时间的最近的5分钟整点
                const roundedMinutes = Math.floor(minutes / 5) * 5;
                const hour = hours.toString().padStart(2, '0');
                const min = roundedMinutes.toString().padStart(2, '0');
                timeString = hour + min;
                console.log(`当前在交易时间内，使用时间: ${timeString}`);
            } else if (isLunchBreak) {
                // 在午休时间，使用11:30
                timeString = "1130";
                console.log("当前在午休时间，使用时间: 11:30");
            } else {
                // 在其他时间，使用15:00
                timeString = "1500";
                console.log("当前在非交易时间，使用时间: 15:00");
            }
            
            // 构建URL
            const url = `http://localhost:5000/api/stocks?plateId=${plateId}&endTime=${timeString}`;
            
            // 如果是自动刷新，更新状态提示
            if (isAutoRefresh) {
                updateRightPanelStatus('自动刷新中...', true);
            }
            
            // 请求数据
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data.length > 0) {
                        // 保存数据到全局变量
                        window.currentRightTableData = data.data;
                        
                        // 显示数据
                        displayStockData(data.data);
                        updateRightPanelStatus(`共显示 ${data.data.length} 只股票 (使用时间: ${timeString.substring(0,2)}:${timeString.substring(2,4)}) ${isAutoRefresh ? '- 自动刷新' : ''}`, false);
                    } else {
                        window.currentRightTableData = [];
                        updateRightPanelStatus(`没有找到股票数据 (使用时间: ${timeString.substring(0,2)}:${timeString.substring(2,4)})`, false);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.currentRightTableData = [];
                    updateRightPanelStatus('获取股票数据出错', false);
                });
        }
        
        // 展示股票数据
        function displayStockData(data) {
            const tbody = document.getElementById('rightTableBody');
            tbody.innerHTML = '';
            
            // 默认按照当前排序状态排序
            sortRightTableData(data);
            
            data.forEach(row => {
                const tr = document.createElement('tr');
                
                RIGHT_TABLE_COLUMNS.forEach((column, colIdx) => {
                    const td = document.createElement('td');
                    let value = row[column.index];
                    
                    // 处理特殊列
                    if (column.name === "主力净额" && value !== undefined) {
                        value = (parseFloat(value) / 10000).toFixed(2);
                        if (parseFloat(value) > 0) td.classList.add('positive');
                        if (parseFloat(value) < 0) td.classList.add('negative');
                    } else if (column.name === "成交额" && value !== undefined) {
                        value = (parseFloat(value) / 100000000).toFixed(2);
                    } else if (column.name === "实际流通" && value !== undefined) {
                        value = (parseFloat(value) / 100000000).toFixed(0);
                    } else if (column.name === "量比" && value !== undefined) {
                        value = parseFloat(value).toFixed(1);
                    } else if (column.name === "实际换手" && value !== undefined) {
                        value = parseFloat(value).toFixed(2);
                    } else if (column.isPercent && value !== undefined) {
                        if (parseFloat(value) > 0) td.classList.add('positive');
                        if (parseFloat(value) < 0) td.classList.add('negative');
                        
                        // 特殊需求：涨幅大于7显示为粉红色
                        if (column.name === "涨幅" && parseFloat(value) > 7) {
                            td.style.color = '#FE218B';
                        }
                    } 
                    
                    // 特殊需求：代码以30开头显示为绿色
                    if (column.name === "代码" && value && value.toString().startsWith('30')) {
                        td.style.color = '#55ff55';
                    }
                    
                    // 板块列宽度固定且文本不换行，可能需要截断过长文本
                    if (column.name === "板块") {
                        td.style.width = '110px';
                        td.style.whiteSpace = 'nowrap';
                        td.style.overflow = 'hidden';
                        td.style.textOverflow = 'ellipsis';
                        td.title = value; // 添加title属性，使鼠标悬浮时显示完整内容
                    }
                    
                    // 给涨幅单元格添加特殊id，用于实时更新
                    if (column.name === "涨幅" && row[0]) {
                        td.id = `increase-${row[0]}`;
                    }
                    
                    // 使股票名称可点击，调用通达信
                    if (column.name === "股票名称") {
                        td.classList.add('clickable');
                        td.style.cursor = 'pointer';
                        td.style.whiteSpace = 'nowrap'; // 添加强制不换行的样式
                        td.style.width = '65px'; // 添加固定宽度
                        td.style.overflow = 'hidden'; // 超出部分隐藏
                        td.style.textOverflow = 'ellipsis'; // 显示省略号
                        td.title = value; // 添加title属性，使鼠标悬浮时显示完整内容
                        td.addEventListener('click', function() {
                            const code = row[0];
                            if (code) {
                                openInTongdaxin(code);
                            }
                        });
                    }
                    
                    td.textContent = value !== undefined ? value : '';
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
            
            // 保存所有股票代码
            const stockCodes = data.map(row => row[0])
                .filter(code => code && 
                    !code.toString().startsWith('8') && 
                    !code.toString().startsWith('4') && 
                    !code.toString().startsWith('9'));
            window.currentStockCodes = stockCodes;
            
            // 如果有股票数据，开始实时更新涨幅
            if (stockCodes.length > 0) {
                startRealtimeQuotesUpdate();
            }
        }
        
        // 在通达信中打开股票
        function openInTongdaxin(code) {
            window.location.href = `http://www.treeid/code_${code}`;
        }
        
        // 开始实时更新涨幅数据
        function startRealtimeQuotesUpdate() {
            // 清除之前的定时器
            if (window.realtimeQuotesTimer) {
                clearInterval(window.realtimeQuotesTimer);
            }
            
            // 使用当前的刷新间隔
            const interval = window.refreshIntervalSeconds || 10;
            
            // 立即更新一次
            updateRealtimeQuotes();
            
            // 设置定时器，按设定的间隔更新
            window.realtimeQuotesTimer = setInterval(updateRealtimeQuotes, interval * 1000);
            
            // 更新状态
            updateRightPanelStatus(`实时涨幅已开始监控（${interval}秒刷新）`, false);
        }
        
        // 更新实时涨幅数据
        function updateRealtimeQuotes() {
            // 如果没有股票代码，则不更新
            if (!window.currentStockCodes || window.currentStockCodes.length === 0) return;
            
            // 过滤股票代码，只保留以0、6、3开头的股票
            const filteredCodes = window.currentStockCodes.filter(code => {
                const codeStr = code.toString();
                return codeStr.startsWith('0') || codeStr.startsWith('6') || codeStr.startsWith('3');
            });
            
            // 输出过滤前后的股票代码数量
            console.log(`涨幅更新: 过滤前股票数量: ${window.currentStockCodes.length}, 过滤后: ${filteredCodes.length}`);
            
            // 如果没有可用的股票代码，则不更新
            if (filteredCodes.length === 0) {
                updateRightPanelStatus('没有符合条件的股票代码可更新', false);
                return;
            }
            
            // 更新状态
            updateRightPanelStatus('正在更新实时涨幅...', true);
            
            // 分批处理股票代码
            const BATCH_SIZE = 500;
            const batches = [];
            for (let i = 0; i < filteredCodes.length; i += BATCH_SIZE) {
                batches.push(filteredCodes.slice(i, i + BATCH_SIZE));
            }
            
            // 存储所有返回的数据
            let allQuotesData = [];
            let completedBatches = 0;
            
            // 处理每一批数据
            batches.forEach((batch, index) => {
                const codesParam = batch.join(',');
                
                // 请求实时涨幅数据
                fetch(`http://localhost:5000/api/realtime_quotes?codes=${codesParam}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.data && data.data.length > 0) {
                            allQuotesData = allQuotesData.concat(data.data);
                        }
                        
                        completedBatches++;
                        
                        // 当所有批次都完成时，更新表格
                        if (completedBatches === batches.length) {
                            // 更新涨幅数据
                            updateIncreaseRateInTable(allQuotesData);
                            
                            // 记录当前排序状态
                            const isRankingByIncrease = rightTableSortState.column === 3; // 3是涨幅列的索引
                            
                            // 仅当当前是按涨幅排序时，才重新按涨幅排序表格
                            if (isRankingByIncrease) {
                                resortTableByIncreaseRate();
                            } else {
                                // 不是按涨幅排序，则根据当前用户选择的列重新排序
                                resortTableByCurrentSetting();
                            }
                            
                            // 更新状态
                            const now = new Date();
                            const timeString = now.getHours().toString().padStart(2, '0') + ':' +
                                now.getMinutes().toString().padStart(2, '0') + ':' +
                                now.getSeconds().toString().padStart(2, '0');
                            
                            updateRightPanelStatus(`实时涨幅已更新 (${timeString}), 共更新${allQuotesData.length}只股票`, false);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        completedBatches++;
                        
                        // 即使出错也要检查是否所有批次都完成
                        if (completedBatches === batches.length) {
                            updateRightPanelStatus('部分数据更新失败', false);
                        }
                    });
            });
        }
        
        // 更新表格中的涨幅数据
        function updateIncreaseRateInTable(quotesData) {
            // 如果没有数据，则不更新
            if (!window.currentRightTableData || window.currentRightTableData.length === 0) return;
            
            // 创建一个映射，用于快速查找股票代码对应的涨幅
            const increaseRateMap = {};
            quotesData.forEach(item => {
                increaseRateMap[item.code] = item.increase_rate;
            });
            
            // 遍历当前表格数据，更新涨幅
            window.currentRightTableData.forEach(row => {
                const code = row[0];
                if (code && increaseRateMap[code] !== undefined) {
                    // 更新内存中的数据
                    row[6] = increaseRateMap[code];
                    
                    // 更新表格中的显示
                    const cell = document.getElementById(`increase-${code}`);
                    if (cell) {
                        const value = increaseRateMap[code];
                        cell.textContent = value;
                        
                        // 更新样式
                        cell.classList.remove('positive', 'negative');
                        if (value > 0) cell.classList.add('positive');
                        if (value < 0) cell.classList.add('negative');
                        
                        // 特殊需求：涨幅大于7显示为粉红色
                        cell.style.color = '';
                        if (value > 7) {
                            cell.style.color = '#FE218B';
                        }
                    }
                }
            });
        }
        
        // 根据涨幅重新排序表格
        function resortTableByIncreaseRate() {
            // 如果没有数据，则不排序
            if (!window.currentRightTableData || window.currentRightTableData.length === 0) return;
            
            // 重置排序状态为涨幅列
            rightTableSortState.column = 3; // 涨幅在UI表格中是第4列（索引3）
            rightTableSortState.direction = 'desc'; // 从大到小排序
            
            // 更新排序指示器
            updateSortIndicators(rightTableSortState.column, rightTableSortState.direction);
            
            // 排序数据
            sortRightTableData(window.currentRightTableData);
            
            // 更新表格显示
            const tbody = document.getElementById('rightTableBody');
            tbody.innerHTML = '';
            
            // 重新显示数据
            displayStockData(window.currentRightTableData);
        }
        
        // 根据当前用户选择的排序设置重新排序表格
        function resortTableByCurrentSetting() {
            // 如果没有数据，则不排序
            if (!window.currentRightTableData || window.currentRightTableData.length === 0) return;
            
            // 使用当前的排序状态进行排序
            sortRightTableData(window.currentRightTableData);
            
            // 更新表格显示
            const tbody = document.getElementById('rightTableBody');
            tbody.innerHTML = '';
            
            // 重新显示数据
            displayStockData(window.currentRightTableData);
        }
        
        // 更新右侧面板状态
        function updateRightPanelStatus(message, showLoading) {
            const statusEl = document.getElementById('rightPanelStatus');
            statusEl.innerHTML = showLoading ? `<span class="loading"></span>${message}` : message;
        }
        
        // 更新表格数据（用于排序时重新显示）
        function updateTable(data) {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';
            
            data.forEach(row => {
                const tr = document.createElement('tr');
                
                row.forEach((cell, index) => {
                    const td = document.createElement('td');
                    td.textContent = cell !== null ? cell : '';
                    
                    // 将板块名称（索引为1）设为可点击
                    if (index === 1) {
                        td.classList.add('clickable');
                        td.style.width = '65px';
                        td.style.whiteSpace = 'nowrap';
                        td.style.overflow = 'hidden';
                        td.style.textOverflow = 'ellipsis';
                        td.title = cell; // 添加title属性，使鼠标悬浮时显示完整内容
                        td.addEventListener('click', () => {
                            const plateCode = row[0]; // 获取板块代码（索引为0）
                            const plateName = cell;   // 板块名称
                            fetchStockData(plateCode, plateName);
                        });
                    }
                    
                    tr.appendChild(td);
                });
                
                tbody.appendChild(tr);
            });
        }

        // 更新时间戳
        function updateTimestamp(timestamp) {
            document.getElementById('timestamp').textContent = `最后更新时间：${timestamp}`;
        }
        
        // 更新状态消息
        function updateStatus(message, showLoading) {
            const statusEl = document.getElementById('status');
            statusEl.innerHTML = showLoading ? `<span class="loading"></span>${message}` : message;
        }
        
        // 排序表格
        function sortTable(columnIndex) {
            if (!currentData || currentData.length === 0) return;
            
            // 从大到小排序
            currentData.sort((a, b) => {
                const valA = a[columnIndex] === null ? -Infinity : a[columnIndex];
                const valB = b[columnIndex] === null ? -Infinity : b[columnIndex];
                
                if (typeof valA === 'number' && typeof valB === 'number') {
                    return valB - valA; // 数字从大到小
                } else {
                    return String(valB).localeCompare(String(valA)); // 字符串从大到小
                }
            });
            
            // 更新表格显示
            updateTable(currentData);
        }

        // 排序右侧表格数据（辅助函数）
        function sortRightTableData(data) {
            // 获取当前排序列的实际数据索引
            const dataIndex = RIGHT_TABLE_COLUMNS[rightTableSortState.column].index;
            
            // 根据排序方向排序
            data.sort((a, b) => {
                let valA = a[dataIndex];
                let valB = b[dataIndex];
                
                // 尝试转换为数字进行比较
                const numA = parseFloat(valA);
                const numB = parseFloat(valB);
                
                if (!isNaN(numA) && !isNaN(numB)) {
                    valA = numA;
                    valB = numB;
                } else {
                    // 对于字符串，使用默认值避免undefined导致比较问题
                    valA = valA || '';
                    valB = valB || '';
                }
                
                // 根据排序方向返回比较结果
                if (rightTableSortState.direction === 'desc') {
                    return typeof valA === 'number' && typeof valB === 'number' ? 
                        valB - valA : // 数字降序
                        String(valB).localeCompare(String(valA)); // 字符串降序
                } else {
                    return typeof valA === 'number' && typeof valB === 'number' ? 
                        valA - valB : // 数字升序
                        String(valA).localeCompare(String(valB)); // 字符串升序
                }
            });
            
            return data;
        }
        
        // 排序右侧表格
        function sortRightTable(columnIndex) {
            const tbody = document.getElementById('rightTableBody');
            if (!tbody.rows || tbody.rows.length === 0) return;
            
            // 使用全局变量记录当前右侧表格的数据
            let rightTableData = window.currentRightTableData || [];
            
            // 切换排序方向
            if (rightTableSortState.column === columnIndex) {
                rightTableSortState.direction = rightTableSortState.direction === 'desc' ? 'asc' : 'desc';
            } else {
                rightTableSortState.column = columnIndex;
                rightTableSortState.direction = 'desc'; // 默认点击新列时是从大到小排序
            }
            
            // 显示当前排序状态
            updateSortIndicators(columnIndex, rightTableSortState.direction);
            
            // 排序数据
            sortRightTableData(rightTableData);
            
            // 更新表格显示
            tbody.innerHTML = '';
            displayStockData(rightTableData);
        }
        
        // 更新表头排序指示器
        function updateSortIndicators(columnIndex, direction) {
            // 先清除所有表头的排序指示器
            const headers = document.querySelectorAll('#rightTable th');
            headers.forEach(header => {
                // 移除可能的排序指示器文本
                header.textContent = header.textContent.replace(' ▲', '').replace(' ▼', '');
            });
            
            // 在当前排序的表头添加排序指示器
            const currentHeader = headers[columnIndex];
            if (currentHeader) {
                const indicator = direction === 'asc' ? ' ▲' : ' ▼';
                currentHeader.textContent = currentHeader.textContent + indicator;
            }
        }

        // 加载保存的刷新间隔设置
        function loadRefreshIntervalSetting() {
            // 从本地存储中获取保存的刷新间隔
            const savedInterval = localStorage.getItem('refreshIntervalSeconds');
            
            if (savedInterval) {
                const interval = parseInt(savedInterval);
                // 确保值有效
                if (!isNaN(interval) && interval >= 3) {
                    // 设置全局变量
                    window.refreshIntervalSeconds = interval;
                    // 更新输入框的值
                    document.getElementById('refreshInterval').value = interval;
                    console.log(`已加载保存的刷新间隔设置: ${interval}秒`);
                } else {
                    // 如果保存的值小于3秒，使用默认值10秒
                    window.refreshIntervalSeconds = 10;
                    document.getElementById('refreshInterval').value = 10;
                    localStorage.setItem('refreshIntervalSeconds', 10);
                    console.log('保存的刷新间隔无效，已重置为默认值: 10秒');
                }
            }
        }
        
        // 设置刷新间隔
        function setRefreshInterval() {
            const input = document.getElementById('refreshInterval');
            let interval = parseInt(input.value);
            
            // 验证输入
            if (isNaN(interval) || interval < 3) {
                alert('请输入有效的刷新间隔（大于等于3秒）');
                input.value = window.refreshIntervalSeconds || 10;
                return;
            }
            
            // 保存新的刷新间隔
            window.refreshIntervalSeconds = interval;
            
            // 保存到本地存储
            localStorage.setItem('refreshIntervalSeconds', interval);
            
            // 更新输入框显示
            input.value = interval;
            
            // 清除之前的定时器
            if (window.realtimeQuotesTimer) {
                clearInterval(window.realtimeQuotesTimer);
            }
            
            // 立即更新一次数据
            updateRealtimeQuotes();
            
            // 设置新的定时器
            window.realtimeQuotesTimer = setInterval(updateRealtimeQuotes, interval * 1000);
            
            // 更新状态提示
            updateRightPanelStatus(`刷新间隔已设置为 ${interval} 秒并已保存`, false);
        }

        // 导出到Excel功能
        function exportToExcel() {
            // 如果没有当前板块信息，提示用户
            if (!window.currentPlateName) {
                alert('请先点击一个板块');
                return;
            }

            // 获取当前时间
            const now = new Date();
            const timeString = now.getFullYear() +
                (now.getMonth() + 1).toString().padStart(2, '0') +
                now.getDate().toString().padStart(2, '0') + '_' +
                now.getHours().toString().padStart(2, '0') +
                now.getMinutes().toString().padStart(2, '0') +
                now.getSeconds().toString().padStart(2, '0');

            // 获取表格数据
            const table = document.getElementById('rightTable');
            const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.replace(/\n/g, ' '));
            const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => 
                Array.from(tr.querySelectorAll('td')).map(td => td.textContent)
            );

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet([headers, ...rows]);

            // 设置列宽
            const colWidths = headers.map(() => ({wch: 15}));
            ws['!cols'] = colWidths;

            // 将工作表添加到工作簿
            XLSX.utils.book_append_sheet(wb, ws, "成分股数据");

            // 生成文件名
            const fileName = `${timeString}_${window.currentPlateName}.xlsx`;

            // 导出文件
            XLSX.writeFile(wb, fileName);

            // 更新状态提示
            updateRightPanelStatus(`数据已导出到 ${fileName}`, false);
        }

        // 初始化表格列拖拽功能
        function initTableColumnDrag() {
            const tables = [document.getElementById('dataTable'), document.getElementById('rightTable')];
            
            tables.forEach(table => {
                const headers = table.querySelectorAll('th');
                let draggedHeader = null;
                
                headers.forEach(header => {
                    header.draggable = true;
                    
                    header.addEventListener('dragstart', function(e) {
                        draggedHeader = this;
                        this.classList.add('dragging');
                        e.dataTransfer.effectAllowed = 'move';
                    });
                    
                    header.addEventListener('dragend', function(e) {
                        this.classList.remove('dragging');
                        headers.forEach(h => h.classList.remove('drag-over'));
                        draggedHeader = null;
                    });
                    
                    header.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        if (this !== draggedHeader) {
                            this.classList.add('drag-over');
                        }
                    });
                    
                    header.addEventListener('dragleave', function(e) {
                        this.classList.remove('drag-over');
                    });
                    
                    header.addEventListener('drop', function(e) {
                        e.preventDefault();
                        this.classList.remove('drag-over');
                        
                        if (this !== draggedHeader) {
                            const allHeaders = [...headers];
                            const draggedIndex = allHeaders.indexOf(draggedHeader);
                            const dropIndex = allHeaders.indexOf(this);
                            
                            // 移动表头
                            if (draggedIndex < dropIndex) {
                                this.parentNode.insertBefore(draggedHeader, this.nextSibling);
                            } else {
                                this.parentNode.insertBefore(draggedHeader, this);
                            }
                            
                            // 移动对应的数据列
                            const tbody = table.querySelector('tbody');
                            const rows = tbody.querySelectorAll('tr');
                            rows.forEach(row => {
                                const cells = row.querySelectorAll('td');
                                const draggedCell = cells[draggedIndex];
                                const dropCell = cells[dropIndex];
                                
                                if (draggedIndex < dropIndex) {
                                    row.insertBefore(draggedCell, dropCell.nextSibling);
                                } else {
                                    row.insertBefore(draggedCell, dropCell);
                                }
                            });
                            
                            // 更新排序状态
                            if (table.id === 'rightTable') {
                                const newIndex = [...headers].indexOf(draggedHeader);
                                if (rightTableSortState.column === draggedIndex) {
                                    rightTableSortState.column = newIndex;
                                } else if (rightTableSortState.column > draggedIndex && rightTableSortState.column <= dropIndex) {
                                    rightTableSortState.column--;
                                } else if (rightTableSortState.column < draggedIndex && rightTableSortState.column >= dropIndex) {
                                    rightTableSortState.column++;
                                }
                                updateSortIndicators(rightTableSortState.column, rightTableSortState.direction);
                            }
                        }
                    });
                });
            });
        }
    </script>
</body>
</html> 