import requests
from openpyxl import Workbook
import os
from datetime import datetime

def download_stock_data(date_str, file_type, folder_path):
    """
    下载指定日期和类型的股票数据
    """
    # URL模板映射
    url_templates = {
        '曾跌停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=5&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
        '跌停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=3&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=4&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
        '竞价': 'https://apphis.longhuvip.com/w1/api/index.php?Filter=0&FilterGem=0&FilterMotherboard=0&FilterTIB=0&Index=0&Is_st=1&Order=1&PhoneOSNew=2&PidType=8&Type=18&VerSion=*******&a=HisDaBanList&apiv=w32&c=HisHomeDingPan&st=60&Day={}',
        '炸板': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=2&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=4&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&',
        '涨停': 'https://apphis.longhuvip.com/w1/api/index.php?Order=1&a=HisDaBanList&st=60&c=HisHomeDingPan&PhoneOSNew=1&DeviceID=00000000-296c-20ad-0000-00003eb74e84&VerSion=********&Index=0&Is_st=1&PidType=1&apiv=w31&Type=6&FilterMotherboard=0&Filter=0&FilterTIB=0&Day={}&FilterGem=0&',
        '自然涨停': 'https://apphis.longhuvip.com/w1/api/index.php?st=60&Is_st=1&FilterGem=0&Index=0&PidType=4&FilterMotherboard=0&Order=1&PhoneOSNew=1&FilterTIB=0&a=HisDaBanList&apiv=w33&Type=6&Filter=0&Day={}&c=HisHomeDingPan&VerSion=********&DeviceID=00000000-4bd0-7c8d-0000-00006d0b2bf7&'
    }

    # 数据处理配置
    data_processing_config = {
        '曾跌停': {
            'columns_to_delete': ['Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 9', 'Column 10',
                'Column 11', 'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
                'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
                'Column 28', 'Column 29', 'Column 30', 'Column 31', 'Column 32', 'Column 33'],
            'header_replacements': {
                'Column 7': '跌停时间',
                'Column 8': '开板时间',
                'Column 13': '主力净额',
                'Column 14': '成交金额',
                'Column 15': '换手率',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7', 'Column 8'],
            'divide_columns': {
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        },
        '跌停': {
            'columns_to_delete': ['Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 8', 'Column 10',
                'Column 11', 'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
                'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
                'Column 28', 'Column 29', 'Column 30', 'Column 31', 'Column 32', 'Column 33'],
            'header_replacements': {
                'Column 7': '跌停时间',
                'Column 9': '封单',
                'Column 13': '主力净额',
                'Column 14': '成交金额',
                'Column 15': '换手率',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7'],
            'divide_columns': {
                'Column 9': 10000,
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        },
        '竞价': {
            'columns_to_delete': [
                'Column 3', 'Column 4', 'Column 6', 'Column 7', 'Column 8', 'Column 9',
                'Column 10', 'Column 11', 'Column 13', 'Column 14', 'Column 15', 'Column 30',
                'Column 17', 'Column 18', 'Column 24', 'Column 25', 'Column 26', 'Column 27',
                'Column 28', 'Column 29', 'Column 31', 'Column 32', 'Column 33'
            ],
            'header_replacements': {
                'Column 19': '涨停委买额',
                'Column 5': '实时涨幅',
                'Column 16': '实际流通',
                'Column 20': '竞价涨幅',
                'Column 21': '竞价净额',
                'Column 22': '竞价换手',
                'Column 23': '竞价成交额'
            },
            'divide_columns': {
                'Column 19': 100000000,
                'Column 21': 10000,
                'Column 23': 10000,
                'Column 16': 100000000
            }
        },
        '炸板': {
            'columns_to_delete': [
                'Column 3', 'Column 4', 'Column 6', 'Column 9', 'Column 11',
                'Column 17', 'Column 18', 'Column 19', 'Column 20', 'Column 21',
                'Column 22', 'Column 23', 'Column 24', 'Column 25', 'Column 26',
                'Column 27', 'Column 28', 'Column 29', 'Column 31', 'Column 32',
                'Column 30', 'Column 33'
            ],
            'header_replacements': {
                'Column 7': '涨停时间',
                'Column 8': '开板时间',
                'Column 13': '主力净额',
                'Column 14': '成交额',
                'Column 15': '实际换手',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7', 'Column 8'],
            'divide_columns': {
                'Column 13': 100000000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        },
        '涨停': {
            'columns_to_filter': [2, 3, 4, 5, 7, 17, 18, 19, 20, 21, 22, 24, 26, 29, 28, 30, 31, 32],
            'header_replacements': {
                "Column 7": "涨停时间",
                "Column 9": "封单",
                "Column 13": "主力净额",
                "Column 14": "成交额",
                "Column 15": "实际换手",
                "Column 16": "实际流通",
                "Column 26": "最后涨停时间",
                "Column 28": "数量",
                "Column 24": "最大封单"
            },
            'timestamp_columns': ['Column 7', 'Column 26'],
            'divide_columns': {
                'Column 9': 10000,
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 24': 10000,
                'Column 16': 100000000
            }
        },
        '自然涨停': {
            'columns_to_delete': [
                'Column 3', 'Column 4', 'Column 5', 'Column 6', 'Column 8', 'Column 24',
                'Column 25', 'Column 26', 'Column 27', 'Column 28', 'Column 29', 'Column 30',
                'Column 18', 'Column 19', 'Column 20', 'Column 21', 'Column 22', 'Column 23',
                'Column 31', 'Column 32', 'Column 33'
            ],
            'header_replacements': {
                'Column 7': '涨停时间',
                'Column 9': '封单',
                'Column 13': '主力净额',
                'Column 14': '成交金额',
                'Column 15': '实际换手',
                'Column 16': '实际流通'
            },
            'timestamp_columns': ['Column 7'],
            'divide_columns': {
                'Column 9': 10000,
                'Column 13': 10000,
                'Column 14': 100000000,
                'Column 16': 100000000
            }
        }
    }

    # 获取对应的URL
    url_base = url_templates[file_type]
    url = url_base.format(date_str)

    # 初始化数据列表
    all_data = []
    index = 0

    # 获取数据
    while True:
        current_url = url + f'&Index={index}'
        response = requests.get(current_url).json()
        
        if 'list' in response:
            data = response['list']
            if not data:
                break
            all_data.extend(data)
            index += 60
        else:
            break

    # 创建Excel文件
    wb = Workbook()
    ws = wb.active

    if all_data:
        if file_type == '涨停':
            # 涨停使用特殊的处理逻辑
            columns_to_filter = data_processing_config[file_type].get('columns_to_filter', [])
            num_columns = len(all_data[0])
            headers = [f'Column {i + 1}' for i in range(num_columns) if i not in columns_to_filter]
            ws.append(headers)

            # 获取列索引映射（针对过滤后的列）
            header_indices = {header: idx for idx, header in enumerate(headers)}

            # 替换表头
            for col_name, new_name in data_processing_config[file_type].get('header_replacements', {}).items():
                if col_name in header_indices:
                    idx = header_indices[col_name]
                    headers[idx] = new_name
                    # 更新工作表中的表头
                    ws.cell(row=1, column=idx+1).value = new_name

            # 查找需要转换时间的列索引
            conversion_time_indices = []
            for col in data_processing_config[file_type].get('timestamp_columns', []):
                new_col = data_processing_config[file_type]['header_replacements'].get(col, col)
                try:
                    idx = headers.index(new_col)
                    conversion_time_indices.append(idx)
                except ValueError:
                    continue

            # 查找需要进行除法运算的列索引及对应的除数
            division_indices = {}
            for col, divisor in data_processing_config[file_type].get('divide_columns', {}).items():
                new_col = data_processing_config[file_type]['header_replacements'].get(col, col)
                try:
                    idx = headers.index(new_col)
                    division_indices[idx] = divisor
                except ValueError:
                    continue

            # 处理数据
            for item in all_data:
                row = [item[i] for i in range(num_columns) if i not in columns_to_filter]

                # 转换时间
                for idx in conversion_time_indices:
                    try:
                        timestamp = int(row[idx])
                        dt = datetime.fromtimestamp(timestamp)
                        row[idx] = dt.strftime('%H:%M')
                    except (ValueError, TypeError):
                        pass

                # 进行除法运算
                for idx, divisor in division_indices.items():
                    try:
                        value = float(row[idx])
                        row[idx] = round(value / divisor, 2)
                    except (ValueError, TypeError):
                        pass

                ws.append(row)

        else:
            # 其他类型使用原来的处理逻辑
            # 写入表头
            headers = [f'Column {i+1}' for i in range(len(all_data[0]))]
            ws.append(headers)

            # 获取当前文件类型的处理配置
            config = data_processing_config.get(file_type, {})
            
            # 获取列索引映射
            header_indices = {header: idx for idx, header in enumerate(headers, 1)}

            # 处理数据
            for row_idx, item in enumerate(all_data, 2):  # 从第2行开始，第1行是表头
                # 处理时间戳
                for col_name in config.get('timestamp_columns', []):
                    if col_name in header_indices:
                        col_idx = header_indices[col_name]
                        try:
                            timestamp = int(item[col_idx-1])
                            dt = datetime.fromtimestamp(timestamp)
                            item[col_idx-1] = dt.strftime('%H:%M')
                        except (ValueError, TypeError):
                            pass

                # 处理需要除法的列
                for col_name, divisor in config.get('divide_columns', {}).items():
                    if col_name in header_indices:
                        col_idx = header_indices[col_name]
                        try:
                            value = float(item[col_idx-1])
                            item[col_idx-1] = round(value / divisor, 2)
                        except (ValueError, TypeError):
                            pass

                ws.append(item)

            # 替换表头
            for col_name, new_name in config.get('header_replacements', {}).items():
                if col_name in header_indices:
                    col_idx = header_indices[col_name]
                    ws.cell(row=1, column=col_idx).value = new_name

            # 删除不需要的列（从后往前删除，避免索引变化）
            columns_to_delete = config.get('columns_to_delete', [])
            columns_to_delete_indices = [header_indices[col] for col in columns_to_delete if col in header_indices]
            for col_idx in sorted(columns_to_delete_indices, reverse=True):
                ws.delete_cols(col_idx)

        # 保存文件
        file_name = f'{date_str}{file_type}.xlsx'
        file_path = os.path.join(folder_path, file_name)
        wb.save(file_path)

    return True 