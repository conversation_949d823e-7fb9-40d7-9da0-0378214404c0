"""
爬虫基类模块
"""
import time
import logging
import requests
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import json
from datetime import datetime

class BaseCrawler(ABC):
    """爬虫基类"""
    
    def __init__(self, name: str, config: Dict):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"crawler.{name}")
        self.session = requests.Session()
        self.results = []
        
    def rate_limit(self):
        """速率限制"""
        if "rate_limit" in self.config:
            time.sleep(self.config["rate_limit"])
            
    def make_request(self, url: str, method: str = "GET", **kwargs) -> Optional[requests.Response]:
        """发送HTTP请求"""
        try:
            self.rate_limit()
            
            if method.upper() == "GET":
                response = self.session.get(url, **kwargs)
            elif method.upper() == "POST":
                response = self.session.post(url, **kwargs)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
            response.raise_for_status()
            return response
            
        except Exception as e:
            self.logger.error(f"请求失败 {url}: {e}")
            return None
            
    @abstractmethod
    def fetch_data(self, **kwargs) -> List[Dict[str, Any]]:
        """获取数据的抽象方法"""
        pass
        
    def save_raw_data(self, data: List[Dict], filename: str = None):
        """保存原始数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{self.name}_{timestamp}.json"
            
        from config.settings import RAW_DATA_DIR
        filepath = RAW_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"原始数据已保存: {filepath}")
            return str(filepath)
        except Exception as e:
            self.logger.error(f"保存原始数据失败: {e}")
            return None
            
    def get_status(self) -> Dict[str, Any]:
        """获取爬虫状态"""
        return {
            "name": self.name,
            "config": self.config,
            "results_count": len(self.results),
            "last_run": getattr(self, 'last_run', None)
        }
        
    def cleanup(self):
        """清理资源"""
        try:
            self.session.close()
        except Exception as e:
            self.logger.error(f"清理Session资源失败: {e}")

class CrawlerManager:
    """爬虫管理器"""
    
    def __init__(self):
        self.crawlers = {}
        self.logger = logging.getLogger("crawler_manager")
        
    def register_crawler(self, crawler: BaseCrawler):
        """注册爬虫"""
        self.crawlers[crawler.name] = crawler
        self.logger.info(f"爬虫已注册: {crawler.name}")
        
    def get_crawler(self, name: str) -> Optional[BaseCrawler]:
        """获取爬虫实例"""
        return self.crawlers.get(name)
        
    def list_crawlers(self) -> List[str]:
        """列出所有爬虫"""
        return list(self.crawlers.keys())
        
    def run_crawler(self, name: str, **kwargs) -> Dict[str, Any]:
        """运行指定爬虫"""
        crawler = self.get_crawler(name)
        if not crawler:
            return {"success": False, "error": f"爬虫不存在: {name}"}
            
        try:
            self.logger.info(f"开始运行爬虫: {name}")
            start_time = datetime.now()
            
            data = crawler.fetch_data(**kwargs)
            crawler.results = data
            crawler.last_run = start_time
            
            # 保存原始数据
            if data:
                crawler.save_raw_data(data)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "success": True,
                "crawler": name,
                "data_count": len(data),
                "duration": duration,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "data": data[:10] if data else []  # 只返回前10条用于预览
            }
            
            self.logger.info(f"爬虫运行完成: {name}, 获取数据: {len(data)}条, 耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"爬虫运行失败 {name}: {e}")
            return {"success": False, "error": str(e)}
        finally:
            crawler.cleanup()
            
    def get_all_status(self) -> Dict[str, Any]:
        """获取所有爬虫状态"""
        return {name: crawler.get_status() for name, crawler in self.crawlers.items()}

# 全局爬虫管理器实例
crawler_manager = CrawlerManager()
